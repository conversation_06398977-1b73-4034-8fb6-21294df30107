import 'package:flutter_test/flutter_test.dart';
import 'package:gather_point/feature/host/data/models/host_dashboard_model.dart';

void main() {
  group('Host Dashboard Integration Tests', () {
    test('HostDashboardModel should parse JSON correctly', () {
      final json = {
        'financial_data': {
          'wallet_balance': 2450.75,
          'total_earnings': 15680.50,
          'pending_earnings': 890.25,
          'total_withdrawn': 12340.00,
          'this_month_earnings': 3650.75,
        },
        'recent_reservations': [
          {
            'id': 1,
            'guest_name': 'أحمد محمد',
            'property_name': 'شقة فاخرة في الرياض',
            'check_in': '2024-01-15',
            'check_out': '2024-01-18',
            'nights': 3,
            'amount': 450.0,
            'status': 'confirmed',
          }
        ],
        'recent_reviews': [
          {
            'id': 1,
            'guest_name': 'فاطمة الزهراء',
            'property_name': 'شقة فاخرة في الرياض',
            'rating': 5.0,
            'comment': 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي',
            'date': '2024-01-10',
          }
        ],
        'statistics': {
          'total_properties': 5,
          'total_reservations': 25,
          'total_reviews': 18,
          'average_rating': 4.7,
          'total_views': 1250,
        },
        'earnings_chart': [
          {'month': 'Jan', 'amount': 1200.0, 'month_number': 1},
          {'month': 'Feb', 'amount': 1800.0, 'month_number': 2},
        ],
        'bookings_chart': [
          {'month': 'Jan', 'count': 8, 'month_number': 1},
          {'month': 'Feb', 'count': 12, 'month_number': 2},
        ],
      };

      final dashboard = HostDashboardModel.fromJson(json);

      expect(dashboard.financialData.walletBalance, 2450.75);
      expect(dashboard.financialData.totalEarnings, 15680.50);
      expect(dashboard.recentReservations.length, 1);
      expect(dashboard.recentReservations.first.guestName, 'أحمد محمد');
      expect(dashboard.recentReviews.length, 1);
      expect(dashboard.recentReviews.first.rating, 5.0);
      expect(dashboard.statistics.totalProperties, 5);
      expect(dashboard.earningsChart.length, 2);
      expect(dashboard.bookingsChart.length, 2);
    });

    test('ReservationSummary should handle different statuses', () {
      final reservation = ReservationSummary(
        id: 1,
        guestName: 'Test Guest',
        propertyName: 'Test Property',
        checkIn: '2024-01-15',
        checkOut: '2024-01-18',
        nights: 3,
        amount: 450.0,
        status: 'confirmed',
      );

      expect(reservation.status, 'confirmed');
      expect(reservation.nights, 3);
      expect(reservation.amount, 450.0);
    });

    test('ReviewSummary should handle rating correctly', () {
      final review = ReviewSummary(
        id: 1,
        guestName: 'Test Guest',
        propertyName: 'Test Property',
        rating: 4.5,
        comment: 'Great place!',
        date: '2024-01-10',
      );

      expect(review.rating, 4.5);
      expect(review.comment, 'Great place!');
    });

    test('HostStatistics should calculate correctly', () {
      final stats = HostStatistics(
        totalProperties: 5,
        totalReservations: 25,
        totalReviews: 18,
        averageRating: 4.7,
        totalViews: 1250,
      );

      expect(stats.totalProperties, 5);
      expect(stats.totalReservations, 25);
      expect(stats.averageRating, 4.7);
    });

    test('Earnings chart data should be accessible', () {
      final chartData = [
        {'month': 'Jan', 'amount': 1200.0, 'month_number': 1},
        {'month': 'Feb', 'amount': 1800.0, 'month_number': 2},
      ];

      expect(chartData.length, 2);
      expect(chartData.first['month'], 'Jan');
      expect(chartData.first['amount'], 1200.0);
      expect(chartData.first['month_number'], 1);
    });
  });

  group('Property Creation Tests', () {
    test('Property creation data should be valid', () {
      final propertyData = {
        'title': 'Test Property',
        'content': 'Test Description',
        'service_category_id': 1,
        'price': 100.0,
        'weekend_price': 120.0,
        'week_price': 650.0,
        'month_price': 2500.0,
        'no_guests': 4,
        'beds': 2,
        'baths': 1,
        'booking_rules': 'Test booking rules',
        'cancelation_rules': 'Test cancellation rules',
        'facility_ids': [1, 2, 3],
        'lat': 24.7136,
        'lon': 46.6753,
      };

      expect(propertyData['title'], 'Test Property');
      expect(propertyData['price'], 100.0);
      expect(propertyData['no_guests'], 4);
      expect(propertyData['facility_ids'], [1, 2, 3]);
      expect(propertyData['lat'], 24.7136);
      expect(propertyData['lon'], 46.6753);
    });
  });
}
