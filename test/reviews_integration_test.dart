import 'package:flutter_test/flutter_test.dart';
import 'package:gather_point/feature/reviews/data/models/review_model.dart';

void main() {
  group('Reviews System Integration Tests', () {
    test('ReviewModel should parse JSON correctly', () {
      final json = {
        'id': 1,
        'user_id': 123,
        'property_id': 456,
        'reservation_id': 789,
        'rating': 4.5,
        'comment': 'Great place to stay!',
        'created_at': '2024-01-15T10:30:00Z',
        'updated_at': '2024-01-15T10:30:00Z',
        'user': {
          'id': 123,
          'name': '<PERSON>',
          'email': '<EMAIL>',
          'avatar': 'https://example.com/avatar.jpg',
        },
        'property': {
          'id': 456,
          'title': 'Beautiful Apartment',
          'image': 'https://example.com/property.jpg',
        },
        'images': [
          {
            'id': 1,
            'review_id': 1,
            'image_url': 'https://example.com/review1.jpg',
            'created_at': '2024-01-15T10:30:00Z',
          }
        ],
      };

      final review = ReviewModel.fromJson(json);

      expect(review.id, 1);
      expect(review.userId, 123);
      expect(review.propertyId, 456);
      expect(review.reservationId, 789);
      expect(review.rating, 4.5);
      expect(review.comment, 'Great place to stay!');
      expect(review.user?.name, 'John Doe');
      expect(review.property?.title, 'Beautiful Apartment');
      expect(review.images?.length, 1);
      expect(review.images?.first.imageUrl, 'https://example.com/review1.jpg');
    });

    test('ReviewStatsModel should parse JSON correctly', () {
      final json = {
        'average_rating': 4.3,
        'total_reviews': 25,
        'rating_distribution': {
          '1': 1,
          '2': 2,
          '3': 5,
          '4': 8,
          '5': 9,
        },
      };

      final stats = ReviewStatsModel.fromJson(json);

      expect(stats.averageRating, 4.3);
      expect(stats.totalReviews, 25);
      expect(stats.ratingDistribution[1], 1);
      expect(stats.ratingDistribution[2], 2);
      expect(stats.ratingDistribution[3], 5);
      expect(stats.ratingDistribution[4], 8);
      expect(stats.ratingDistribution[5], 9);
    });

    test('CreateReviewRequest should serialize correctly', () {
      final request = CreateReviewRequest(
        propertyId: 456,
        reservationId: 789,
        rating: 4.5,
        comment: 'Great experience!',
        imagePaths: ['path1.jpg', 'path2.jpg'],
      );

      final json = request.toJson();

      expect(json['property_id'], 456);
      expect(json['reservation_id'], 789);
      expect(json['rating'], 4.5);
      expect(json['comment'], 'Great experience!');
      expect(json['image_paths'], ['path1.jpg', 'path2.jpg']);
    });

    test('UserModel should handle missing optional fields', () {
      final json = {
        'id': 123,
        'name': 'John Doe',
        // email, phone, avatar are missing
      };

      final user = UserModel.fromJson(json);

      expect(user.id, 123);
      expect(user.name, 'John Doe');
      expect(user.email, null);
      expect(user.phone, null);
      expect(user.avatar, null);
    });

    test('PropertyModel should parse minimal data', () {
      final json = {
        'id': 456,
        'title': 'Test Property',
        // image is missing
      };

      final property = PropertyModel.fromJson(json);

      expect(property.id, 456);
      expect(property.title, 'Test Property');
      expect(property.image, null);
    });

    test('ReviewImageModel should parse correctly', () {
      final json = {
        'id': 1,
        'review_id': 123,
        'image_url': 'https://example.com/image.jpg',
        'created_at': '2024-01-15T10:30:00Z',
      };

      final image = ReviewImageModel.fromJson(json);

      expect(image.id, 1);
      expect(image.reviewId, 123);
      expect(image.imageUrl, 'https://example.com/image.jpg');
      expect(image.createdAt.year, 2024);
      expect(image.createdAt.month, 1);
      expect(image.createdAt.day, 15);
    });

    test('ReviewModel should handle empty relationships', () {
      final json = {
        'id': 1,
        'user_id': 123,
        'property_id': 456,
        'reservation_id': 789,
        'rating': 4.0,
        'comment': 'Good place',
        'created_at': '2024-01-15T10:30:00Z',
        'updated_at': '2024-01-15T10:30:00Z',
        // user, property, images are missing
      };

      final review = ReviewModel.fromJson(json);

      expect(review.id, 1);
      expect(review.rating, 4.0);
      expect(review.comment, 'Good place');
      expect(review.user, null);
      expect(review.property, null);
      expect(review.images, null);
    });

    test('ReviewStatsModel should handle empty rating distribution', () {
      final json = {
        'average_rating': 0.0,
        'total_reviews': 0,
        'rating_distribution': {},
      };

      final stats = ReviewStatsModel.fromJson(json);

      expect(stats.averageRating, 0.0);
      expect(stats.totalReviews, 0);
      expect(stats.ratingDistribution.isEmpty, true);
    });

    test('CreateReviewRequest should handle missing image paths', () {
      final request = CreateReviewRequest(
        propertyId: 456,
        reservationId: 789,
        rating: 5.0,
        comment: 'Excellent!',
        // imagePaths is null
      );

      final json = request.toJson();

      expect(json['property_id'], 456);
      expect(json['reservation_id'], 789);
      expect(json['rating'], 5.0);
      expect(json['comment'], 'Excellent!');
      expect(json.containsKey('image_paths'), false);
    });

    test('ReviewModel toJson should work correctly', () {
      final review = ReviewModel(
        id: 1,
        userId: 123,
        propertyId: 456,
        reservationId: 789,
        rating: 4.5,
        comment: 'Great place!',
        createdAt: DateTime(2024, 1, 15, 10, 30),
        updatedAt: DateTime(2024, 1, 15, 10, 30),
      );

      final json = review.toJson();

      expect(json['id'], 1);
      expect(json['user_id'], 123);
      expect(json['property_id'], 456);
      expect(json['reservation_id'], 789);
      expect(json['rating'], 4.5);
      expect(json['comment'], 'Great place!');
      expect(json['created_at'], '2024-01-15T10:30:00.000');
      expect(json['updated_at'], '2024-01-15T10:30:00.000');
    });

    test('ReviewStatsModel toJson should work correctly', () {
      final stats = ReviewStatsModel(
        averageRating: 4.3,
        totalReviews: 25,
        ratingDistribution: {1: 1, 2: 2, 3: 5, 4: 8, 5: 9},
      );

      final json = stats.toJson();

      expect(json['average_rating'], 4.3);
      expect(json['total_reviews'], 25);
      expect(json['rating_distribution']['1'], 1);
      expect(json['rating_distribution']['2'], 2);
      expect(json['rating_distribution']['3'], 5);
      expect(json['rating_distribution']['4'], 8);
      expect(json['rating_distribution']['5'], 9);
    });
  });

  group('Property Creation Tests', () {
    test('Property creation with location should work', () {
      final propertyData = {
        'title': 'Luxury Villa',
        'content': 'Beautiful villa with amazing view',
        'service_category_id': 1,
        'price': 200.0,
        'weekend_price': 250.0,
        'week_price': 1200.0,
        'month_price': 4500.0,
        'no_guests': 6,
        'beds': 3,
        'baths': 2,
        'booking_rules': 'Check-in after 3 PM',
        'cancelation_rules': 'Free cancellation 24h before',
        'facility_ids': [1, 2, 3, 4],
        'lat': 24.7136,
        'lon': 46.6753,
      };

      expect(propertyData['title'], 'Luxury Villa');
      expect(propertyData['price'], 200.0);
      expect(propertyData['weekend_price'], 250.0);
      expect(propertyData['no_guests'], 6);
      expect(propertyData['facility_ids'], [1, 2, 3, 4]);
      expect(propertyData['lat'], 24.7136);
      expect(propertyData['lon'], 46.6753);
    });

    test('Property creation with minimal data should work', () {
      final propertyData = {
        'title': 'Simple Apartment',
        'content': 'Basic apartment',
        'service_category_id': 2,
        'price': 100.0,
        'no_guests': 2,
        'beds': 1,
        'baths': 1,
        'facility_ids': <int>[],
      };

      expect(propertyData['title'], 'Simple Apartment');
      expect(propertyData['price'], 100.0);
      expect(propertyData['facility_ids'], <int>[]);
      expect(propertyData.containsKey('weekend_price'), false);
      expect(propertyData.containsKey('lat'), false);
    });
  });
}
