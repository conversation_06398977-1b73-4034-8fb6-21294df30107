<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party products such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'msegat' => [
        'token' => env('MSEGAT_TOKEN', '07158616c9f86e5662ff28cb5d4f6d06'),
        'username' => env('MSEGAT_USERNAME', 'GatherPoint'),
        'sender' => env('MSEGAT_SENDER', 'GatherPoint'),
        'api_url' => env('MSEGAT_API_URL', 'https://www.msegat.com/gw/sendsms.php'),
    ],

];
