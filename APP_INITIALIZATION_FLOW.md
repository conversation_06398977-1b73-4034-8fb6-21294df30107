# App Initialization Flow Documentation

## Overview

This document describes the enhanced app initialization flow that handles onboarding, guest token management, and proper navigation based on user state.

## Flow Description

### 1. **First Time User (No Onboarding Viewed)**
```
App Launch → Splash Screen → Onboarding → Generate Guest Token → Navigate to Home
```

### 2. **Returning User (Onboarding Completed)**
```
App Launch → Splash Screen → Check Existing Token → Validate Token → Navigate to Home
```

### 3. **Invalid/Expired Token**
```
App Launch → Splash Screen → Token Validation Fails → Generate New Guest Token → Navigate to Home
```

## Implementation Details

### AppInitializationCubit

**Location**: `lib/core/managers/app_initialization_cubit/`

**States**:
- `AppInitializationInitial` - Initial state
- `AppInitializationLoading` - Processing initialization
- `AppInitializationShowOnboarding` - Navigate to onboarding
- `AppInitializationNavigateToHome` - Navigate to home with user data
- `AppInitializationError` - Error occurred during initialization

**Methods**:
- `initializeApp()` - Main initialization logic
- `completeOnboarding()` - Handle onboarding completion
- `refreshToken()` - Force refresh guest token
- `resetAppState()` - Reset app state (for logout)

### Enhanced Components

#### 1. **Splash Screen** (`splash_view_body.dart`)
- Shows splash animation for 2.5 seconds
- Triggers app initialization via `AppInitializationCubit`
- Listens for initialization states and navigates accordingly

#### 2. **Onboarding** (`on_boarding_view.dart`)
- Uses `AppInitializationCubit.completeOnboarding()` on skip/complete
- Listens for navigation state to redirect to home
- Automatically generates guest token after completion

#### 3. **Main App** (`main.dart`)
- Provides `AppInitializationCubit` to the widget tree
- Always starts with splash screen
- Removed hardcoded onboarding check from router

## User Experience Flow

### First Time User Journey:
1. **App Launch**: User opens app for the first time
2. **Splash Screen**: Shows for 2.5 seconds with animation
3. **Initialization Check**: App checks if onboarding was viewed (false)
4. **Onboarding**: User sees onboarding screens
5. **Skip/Complete**: User completes onboarding
6. **Guest Token**: App automatically generates guest token
7. **Home Navigation**: User is taken to home screen as guest
8. **Onboarding Flag**: Marked as completed for future launches

### Returning User Journey:
1. **App Launch**: User opens app (not first time)
2. **Splash Screen**: Shows for 2.5 seconds with animation
3. **Token Check**: App checks for existing user token
4. **Token Validation**: Validates token with backend
5. **Success**: If valid, navigate to home with user data
6. **Failure**: If invalid, generate new guest token and navigate to home

## Technical Implementation

### State Management
```dart
// Initialize app
context.read<AppInitializationCubit>().initializeApp();

// Complete onboarding
context.read<AppInitializationCubit>().completeOnboarding();

// Listen for state changes
BlocListener<AppInitializationCubit, AppInitializationState>(
  listener: (context, state) {
    if (state is AppInitializationNavigateToHome) {
      GoRouter.of(context).go(RoutesKeys.kHomeViewTab);
    }
  },
)
```

### Storage Management
- **Onboarding Status**: Stored in `AppConstants.kOnboardingCompletedKey`
- **User Data**: Stored in `AppConstants.kMyProfileKey`
- **Settings**: Stored in `AppConstants.kSettingsBoxName`

### Error Handling
- Network failures during token validation
- Backend API errors
- Storage access issues
- Fallback to guest token generation

## Benefits

### 1. **Seamless User Experience**
- No manual login required for browsing
- Automatic guest access
- Smooth onboarding flow

### 2. **Robust Token Management**
- Automatic token validation
- Graceful handling of expired tokens
- Fallback to guest mode

### 3. **Proper State Management**
- Centralized initialization logic
- Clear separation of concerns
- Predictable navigation flow

### 4. **Error Resilience**
- Graceful degradation on errors
- Always provides app access
- Clear error states

## Testing Scenarios

### Test Case 1: First Time User
1. Clear app data
2. Launch app
3. Verify splash screen appears
4. Verify onboarding screens appear
5. Complete onboarding
6. Verify navigation to home
7. Verify guest token is generated

### Test Case 2: Returning User with Valid Token
1. Launch app with existing valid token
2. Verify splash screen appears
3. Verify direct navigation to home
4. Verify user data is preserved

### Test Case 3: Returning User with Invalid Token
1. Launch app with expired/invalid token
2. Verify splash screen appears
3. Verify new guest token is generated
4. Verify navigation to home as guest

### Test Case 4: Network Error Handling
1. Launch app without internet
2. Verify fallback to guest mode
3. Verify app remains functional

## Configuration

### Environment Setup
No additional configuration required. The flow uses existing:
- Hive storage boxes
- API endpoints
- Authentication services

### Dependencies
- `flutter_bloc` for state management
- `go_router` for navigation
- `hive` for local storage
- Existing auth use cases

## Monitoring and Analytics

### Key Metrics to Track
- Onboarding completion rate
- Token validation success rate
- Guest token generation frequency
- Navigation flow completion time

### Error Tracking
- Token validation failures
- Network connectivity issues
- Storage access problems
- Navigation errors

## Future Enhancements

### Potential Improvements
1. **Biometric Authentication**: Add fingerprint/face ID support
2. **Offline Mode**: Enhanced offline capabilities
3. **Progressive Onboarding**: Context-aware feature introduction
4. **A/B Testing**: Different onboarding flows
5. **Analytics Integration**: Detailed user journey tracking

## Troubleshooting

### Common Issues
1. **Stuck on Splash**: Check network connectivity and API availability
2. **Onboarding Loop**: Verify storage permissions and Hive box access
3. **Navigation Errors**: Check route definitions and context availability
4. **Token Issues**: Verify backend API endpoints and authentication flow

### Debug Tools
- Enable debug logging in `AppInitializationCubit`
- Monitor Hive storage state
- Check network requests in development
- Use Flutter Inspector for widget tree analysis
