<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReviewLike extends Model
{
    use HasFactory;

    protected $fillable = [
        'review_id',
        'user_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the review that was liked
     */
    public function review()
    {
        return $this->belongsTo(Review::class);
    }

    /**
     * Get the user who liked the review
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
