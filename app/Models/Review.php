<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Review extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'property_id',
        'reservation_id',
        'rating',
        'comment',
        'is_approved',
        'reported_count',
    ];

    protected $casts = [
        'rating' => 'decimal:1',
        'is_approved' => 'boolean',
        'reported_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $with = ['user', 'property', 'images'];

    /**
     * Get the user who wrote the review
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the property being reviewed
     */
    public function property()
    {
        return $this->belongsTo(ServiceCategoryItem::class, 'property_id');
    }

    /**
     * Get the reservation this review is for
     */
    public function reservation()
    {
        return $this->belongsTo(ServiceCategoryItemReservation::class, 'reservation_id');
    }

    /**
     * Get the review images
     */
    public function images()
    {
        return $this->hasMany(ReviewImage::class);
    }

    /**
     * Get the review likes
     */
    public function likes()
    {
        return $this->hasMany(ReviewLike::class);
    }

    /**
     * Get the review reports
     */
    public function reports()
    {
        return $this->hasMany(ReviewReport::class);
    }

    /**
     * Check if user has liked this review
     */
    public function isLikedBy($userId)
    {
        return $this->likes()->where('user_id', $userId)->exists();
    }

    /**
     * Get likes count
     */
    public function getLikesCountAttribute()
    {
        return $this->likes()->count();
    }

    /**
     * Scope for approved reviews only
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope for reviews by rating
     */
    public function scopeByRating($query, $minRating = null, $maxRating = null)
    {
        if ($minRating !== null) {
            $query->where('rating', '>=', $minRating);
        }
        
        if ($maxRating !== null) {
            $query->where('rating', '<=', $maxRating);
        }
        
        return $query;
    }

    /**
     * Scope for sorting reviews
     */
    public function scopeSorted($query, $sortBy = 'newest')
    {
        switch ($sortBy) {
            case 'oldest':
                return $query->orderBy('created_at', 'asc');
            case 'highest_rated':
                return $query->orderBy('rating', 'desc');
            case 'lowest_rated':
                return $query->orderBy('rating', 'asc');
            case 'most_liked':
                return $query->withCount('likes')->orderBy('likes_count', 'desc');
            case 'newest':
            default:
                return $query->orderBy('created_at', 'desc');
        }
    }

    /**
     * Get average rating for a property
     */
    public static function getAverageRating($propertyId)
    {
        return static::where('property_id', $propertyId)
            ->approved()
            ->avg('rating') ?? 0;
    }

    /**
     * Get rating distribution for a property
     */
    public static function getRatingDistribution($propertyId)
    {
        $distribution = [];
        
        for ($rating = 1; $rating <= 5; $rating++) {
            $count = static::where('property_id', $propertyId)
                ->approved()
                ->where('rating', $rating)
                ->count();
            
            $distribution[$rating] = $count;
        }
        
        return $distribution;
    }

    /**
     * Check if user can review a property
     */
    public static function canUserReview($userId, $propertyId, $reservationId)
    {
        // Check if user has a completed reservation for this property
        $reservation = ServiceCategoryItemReservation::where('id', $reservationId)
            ->where('user_id', $userId)
            ->where('service_category_item_id', $propertyId)
            ->where('confirmed', true)
            ->where('reservation_to', '<', now())
            ->first();

        if (!$reservation) {
            return false;
        }

        // Check if user hasn't already reviewed this reservation
        $existingReview = static::where('user_id', $userId)
            ->where('property_id', $propertyId)
            ->where('reservation_id', $reservationId)
            ->first();

        return !$existingReview;
    }
}
