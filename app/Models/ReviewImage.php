<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReviewImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'review_id',
        'image_url',
        'image_path',
        'order',
    ];

    protected $casts = [
        'order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the review that owns this image
     */
    public function review()
    {
        return $this->belongsTo(Review::class);
    }

    /**
     * Get the full image URL
     */
    public function getImageUrlAttribute($value)
    {
        if ($value) {
            // If it's already a full URL, return as is
            if (filter_var($value, FILTER_VALIDATE_URL)) {
                return $value;
            }
            
            // Otherwise, prepend the app URL
            return url($value);
        }
        
        return null;
    }
}
