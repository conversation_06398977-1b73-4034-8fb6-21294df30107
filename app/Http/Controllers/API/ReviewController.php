<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\ReviewImage;
use App\Models\ReviewLike;
use App\Models\ReviewReport;
use App\Models\ServiceCategoryItem;
use App\Models\ServiceCategoryItemReservation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    /**
     * Get reviews for a property
     */
    public function index(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'property_id' => 'required|integer|exists:service_category_items,id',
                'page' => 'integer|min:1',
                'limit' => 'integer|min:1|max:50',
                'min_rating' => 'numeric|min:1|max:5',
                'sort_by' => 'string|in:newest,oldest,highest_rated,lowest_rated,most_liked',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $propertyId = $request->property_id;
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            $minRating = $request->min_rating;
            $sortBy = $request->get('sort_by', 'newest');

            $query = Review::where('property_id', $propertyId)
                ->approved()
                ->with(['user', 'images']);

            if ($minRating) {
                $query->byRating($minRating);
            }

            $query->sorted($sortBy);

            $reviews = $query->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'data' => $reviews->items(),
                'pagination' => [
                    'current_page' => $reviews->currentPage(),
                    'total_pages' => $reviews->lastPage(),
                    'total_items' => $reviews->total(),
                    'per_page' => $reviews->perPage(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch reviews',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get review statistics for a property
     */
    public function stats($propertyId)
    {
        try {
            $property = ServiceCategoryItem::findOrFail($propertyId);

            $averageRating = Review::getAverageRating($propertyId);
            $totalReviews = Review::where('property_id', $propertyId)->approved()->count();
            $ratingDistribution = Review::getRatingDistribution($propertyId);

            return response()->json([
                'success' => true,
                'data' => [
                    'average_rating' => round($averageRating, 1),
                    'total_reviews' => $totalReviews,
                    'rating_distribution' => $ratingDistribution,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch review stats',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a new review
     */
    public function store(Request $request)
    {
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'property_id' => 'required|integer|exists:service_category_items,id',
                'reservation_id' => 'required|integer|exists:service_category_item_reservations,id',
                'rating' => 'required|numeric|min:1|max:5',
                'comment' => 'required|string|min:10|max:1000',
                'images' => 'array|max:5',
                'images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Check if user can review this property
            if (!Review::canUserReview($user->id, $request->property_id, $request->reservation_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not eligible to review this property',
                ], 403);
            }

            DB::beginTransaction();

            // Create the review
            $review = Review::create([
                'user_id' => $user->id,
                'property_id' => $request->property_id,
                'reservation_id' => $request->reservation_id,
                'rating' => $request->rating,
                'comment' => $request->comment,
                'is_approved' => true, // Auto-approve for now
            ]);

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $path = $image->store('reviews', 'public');
                    
                    ReviewImage::create([
                        'review_id' => $review->id,
                        'image_url' => Storage::url($path),
                        'image_path' => $path,
                        'order' => $index + 1,
                    ]);
                }
            }

            DB::commit();

            // Load the review with relationships
            $review->load(['user', 'images']);

            return response()->json([
                'success' => true,
                'message' => 'Review created successfully',
                'data' => $review,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create review',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a review
     */
    public function update(Request $request, $reviewId)
    {
        try {
            $user = Auth::user();
            $review = Review::where('id', $reviewId)
                ->where('user_id', $user->id)
                ->firstOrFail();

            $validator = Validator::make($request->all(), [
                'rating' => 'numeric|min:1|max:5',
                'comment' => 'string|min:10|max:1000',
                'new_images' => 'array|max:5',
                'new_images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
                'remove_image_ids' => 'array',
                'remove_image_ids.*' => 'integer|exists:review_images,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            DB::beginTransaction();

            // Update review fields
            $updateData = [];
            if ($request->has('rating')) {
                $updateData['rating'] = $request->rating;
            }
            if ($request->has('comment')) {
                $updateData['comment'] = $request->comment;
            }

            if (!empty($updateData)) {
                $review->update($updateData);
            }

            // Remove specified images
            if ($request->has('remove_image_ids')) {
                $imagesToRemove = ReviewImage::where('review_id', $review->id)
                    ->whereIn('id', $request->remove_image_ids)
                    ->get();

                foreach ($imagesToRemove as $image) {
                    if ($image->image_path) {
                        Storage::disk('public')->delete($image->image_path);
                    }
                    $image->delete();
                }
            }

            // Add new images
            if ($request->hasFile('new_images')) {
                $currentImageCount = $review->images()->count();
                
                foreach ($request->file('new_images') as $index => $image) {
                    $path = $image->store('reviews', 'public');
                    
                    ReviewImage::create([
                        'review_id' => $review->id,
                        'image_url' => Storage::url($path),
                        'image_path' => $path,
                        'order' => $currentImageCount + $index + 1,
                    ]);
                }
            }

            DB::commit();

            // Load the updated review with relationships
            $review->load(['user', 'images']);

            return response()->json([
                'success' => true,
                'message' => 'Review updated successfully',
                'data' => $review,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update review',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a review
     */
    public function destroy($reviewId)
    {
        try {
            $user = Auth::user();
            $review = Review::where('id', $reviewId)
                ->where('user_id', $user->id)
                ->firstOrFail();

            DB::beginTransaction();

            // Delete associated images
            foreach ($review->images as $image) {
                if ($image->image_path) {
                    Storage::disk('public')->delete($image->image_path);
                }
                $image->delete();
            }

            // Delete the review
            $review->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Review deleted successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete review',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if user can review a property
     */
    public function canReview(Request $request)
    {
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'property_id' => 'required|integer|exists:service_category_items,id',
                'reservation_id' => 'required|integer|exists:service_category_item_reservations,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $canReview = Review::canUserReview(
                $user->id,
                $request->property_id,
                $request->reservation_id
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'can_review' => $canReview,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check review eligibility',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
