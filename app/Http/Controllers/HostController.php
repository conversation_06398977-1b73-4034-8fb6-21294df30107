<?php

namespace App\Http\Controllers;

use App\Models\ServiceCategoryItem;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class HostController extends Controller
{
    /**
     * Get host dashboard data
     */
    public function dashboard(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Get host's properties
            $properties = ServiceCategoryItem::where('user_id', $user->id)->get();
            $propertyIds = $properties->pluck('id');
            
            // Financial data
            $financialData = $this->getFinancialData($user, $propertyIds);
            
            // Recent reservations (last 5)
            $recentReservations = $this->getRecentReservations($propertyIds);
            
            // Recent reviews (last 5)
            $recentReviews = $this->getRecentReviews($propertyIds);
            
            // Statistics
            $statistics = $this->getHostStatistics($user, $propertyIds);
            
            // Charts data
            $earningsChart = $this->getEarningsChart($propertyIds);
            $bookingsChart = $this->getBookingsChart($propertyIds);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'financial_data' => $financialData,
                    'recent_reservations' => $recentReservations,
                    'recent_reviews' => $recentReviews,
                    'statistics' => $statistics,
                    'earnings_chart' => $earningsChart,
                    'bookings_chart' => $bookingsChart,
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch dashboard data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get financial summary for host
     */
    public function financialSummary(Request $request)
    {
        try {
            $user = Auth::user();
            $properties = ServiceCategoryItem::where('user_id', $user->id)->get();
            $propertyIds = $properties->pluck('id');
            
            $financialData = $this->getFinancialData($user, $propertyIds);
            
            return response()->json([
                'success' => true,
                'data' => $financialData
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch financial data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get host's reservations
     */
    public function reservations(Request $request)
    {
        try {
            $user = Auth::user();
            $properties = ServiceCategoryItem::where('user_id', $user->id)->get();
            $propertyIds = $properties->pluck('id');
            
            $query = Reservation::whereIn('service_category_item_id', $propertyIds)
                ->with(['item', 'user']);
            
            // Filter by status if provided
            if ($request->has('status')) {
                $query->where('confirmed', $request->status === 'confirmed');
            }
            
            // Pagination
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            
            $reservations = $query->orderBy('created_at', 'desc')
                ->paginate($limit, ['*'], 'page', $page);
            
            return response()->json([
                'success' => true,
                'data' => $reservations->items(),
                'pagination' => [
                    'current_page' => $reservations->currentPage(),
                    'total_pages' => $reservations->lastPage(),
                    'total_items' => $reservations->total(),
                    'per_page' => $reservations->perPage(),
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch reservations',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get host's reviews
     */
    public function reviews(Request $request)
    {
        try {
            $user = Auth::user();
            $properties = ServiceCategoryItem::where('user_id', $user->id)->get();
            $propertyIds = $properties->pluck('id');
            
            // For now, return dummy data since reviews table doesn't exist
            // TODO: Implement when reviews table is created
            $reviews = collect([
                [
                    'id' => 1,
                    'guest_name' => 'فاطمة الزهراء',
                    'property_name' => 'شقة فاخرة في الرياض',
                    'rating' => 5.0,
                    'comment' => 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي',
                    'date' => '2024-01-10',
                ],
                [
                    'id' => 2,
                    'guest_name' => 'عبدالله السعد',
                    'property_name' => 'فيلا مع مسبح',
                    'rating' => 4.5,
                    'comment' => 'إقامة جميلة، المسبح رائع والفيلا واسعة',
                    'date' => '2024-01-08',
                ],
            ]);
            
            return response()->json([
                'success' => true,
                'data' => $reviews
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get financial data for host
     */
    private function getFinancialData($user, $propertyIds)
    {
        // Calculate earnings from confirmed reservations
        $confirmedReservations = Reservation::whereIn('service_category_item_id', $propertyIds)
            ->where('confirmed', true)
            ->with('item')
            ->get();
        
        $totalEarnings = 0;
        $thisMonthEarnings = 0;
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;
        
        foreach ($confirmedReservations as $reservation) {
            if ($reservation->item) {
                $days = Carbon::parse($reservation->reservation_to)
                    ->diffInDays(Carbon::parse($reservation->reservation_from));
                $amount = $reservation->item->price * max($days, 1);
                $totalEarnings += $amount;
                
                // Check if reservation is from current month
                $reservationMonth = Carbon::parse($reservation->created_at)->month;
                $reservationYear = Carbon::parse($reservation->created_at)->year;
                
                if ($reservationMonth === $currentMonth && $reservationYear === $currentYear) {
                    $thisMonthEarnings += $amount;
                }
            }
        }
        
        // For demo purposes, calculate other values
        $walletBalance = $totalEarnings * 0.15; // 15% available for withdrawal
        $pendingEarnings = $totalEarnings * 0.05; // 5% pending
        $totalWithdrawn = $totalEarnings * 0.80; // 80% already withdrawn
        
        return [
            'wallet_balance' => round($walletBalance, 2),
            'total_earnings' => round($totalEarnings, 2),
            'pending_earnings' => round($pendingEarnings, 2),
            'total_withdrawn' => round($totalWithdrawn, 2),
            'this_month_earnings' => round($thisMonthEarnings, 2),
        ];
    }
    
    /**
     * Get recent reservations for host
     */
    private function getRecentReservations($propertyIds)
    {
        return Reservation::whereIn('service_category_item_id', $propertyIds)
            ->with(['item', 'user'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($reservation) {
                $days = Carbon::parse($reservation->reservation_to)
                    ->diffInDays(Carbon::parse($reservation->reservation_from));
                $amount = $reservation->item ? $reservation->item->price * max($days, 1) : 0;
                
                return [
                    'id' => $reservation->id,
                    'guest_name' => $reservation->user ? $reservation->user->name : 'Unknown',
                    'property_name' => $reservation->item ? $reservation->item->title : 'Unknown Property',
                    'check_in' => $reservation->reservation_from,
                    'check_out' => $reservation->reservation_to,
                    'nights' => max($days, 1),
                    'amount' => round($amount, 2),
                    'status' => $reservation->confirmed ? 'confirmed' : 'processing',
                ];
            });
    }
    
    /**
     * Get recent reviews for host
     */
    private function getRecentReviews($propertyIds)
    {
        // Return dummy data for now
        // TODO: Implement when reviews table is created
        return collect([
            [
                'id' => 1,
                'guest_name' => 'فاطمة الزهراء',
                'property_name' => 'شقة فاخرة في الرياض',
                'rating' => 5.0,
                'comment' => 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي',
                'date' => '2024-01-10',
            ],
            [
                'id' => 2,
                'guest_name' => 'عبدالله السعد',
                'property_name' => 'فيلا مع مسبح',
                'rating' => 4.5,
                'comment' => 'إقامة جميلة، المسبح رائع والفيلا واسعة',
                'date' => '2024-01-08',
            ],
        ])->take(5);
    }
    
    /**
     * Get host statistics
     */
    private function getHostStatistics($user, $propertyIds)
    {
        $totalProperties = count($propertyIds);
        $totalReservations = Reservation::whereIn('service_category_item_id', $propertyIds)->count();
        $totalReviews = 0; // TODO: Implement when reviews table exists
        $averageRating = 4.7; // TODO: Calculate from actual reviews
        $totalViews = ServiceCategoryItem::whereIn('id', $propertyIds)->sum('views');
        
        return [
            'total_properties' => $totalProperties,
            'total_reservations' => $totalReservations,
            'total_reviews' => $totalReviews,
            'average_rating' => $averageRating,
            'total_views' => $totalViews,
        ];
    }
    
    /**
     * Get earnings chart data (last 6 months)
     */
    private function getEarningsChart($propertyIds)
    {
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();
            
            $earnings = Reservation::whereIn('service_category_item_id', $propertyIds)
                ->where('confirmed', true)
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->with('item')
                ->get()
                ->sum(function ($reservation) {
                    if ($reservation->item) {
                        $days = Carbon::parse($reservation->reservation_to)
                            ->diffInDays(Carbon::parse($reservation->reservation_from));
                        return $reservation->item->price * max($days, 1);
                    }
                    return 0;
                });
            
            $months[] = [
                'month' => $date->format('M'),
                'amount' => round($earnings, 2),
                'month_number' => $date->month,
            ];
        }
        
        return $months;
    }
    
    /**
     * Get bookings chart data (last 6 months)
     */
    private function getBookingsChart($propertyIds)
    {
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();
            
            $count = Reservation::whereIn('service_category_item_id', $propertyIds)
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->count();
            
            $months[] = [
                'month' => $date->format('M'),
                'count' => $count,
                'month_number' => $date->month,
            ];
        }
        
        return $months;
    }
}
