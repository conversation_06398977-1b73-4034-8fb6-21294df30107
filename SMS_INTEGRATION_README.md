# SMS Integration with Msegat Gateway

This document describes the SMS integration implementation for OTP authentication using the Msegat SMS gateway.

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```env
# Msegat SMS Gateway Configuration
MSEGAT_TOKEN=07158616c9f86e5662ff28cb5d4f6d06
MSEGAT_USERNAME=GatherPoint
MSEGAT_SENDER=GatherPoint
MSEGAT_API_URL=https://www.msegat.com/gw/sendsms.php
```

### Service Configuration

The SMS service is configured in `config/services.php`:

```php
'msegat' => [
    'token' => env('MSEGAT_TOKEN', '07158616c9f86e5662ff28cb5d4f6d06'),
    'username' => env('MSEGAT_USERNAME', 'GatherPoint'),
    'sender' => env('MSEGAT_SENDER', 'GatherPoint'),
    'api_url' => env('MSEGAT_API_URL', 'https://www.msegat.com/gw/sendsms.php'),
],
```

## Implementation

### SMS Service Class

The `MsegatSmsService` class handles all SMS operations:

- **Location**: `app/Services/SMS/MsegatSmsService.php`
- **Features**:
  - Send SMS messages
  - Send OTP codes
  - Phone number validation and formatting
  - Delivery status checking
  - Account balance checking

### Phone Number Format

The service supports Saudi Arabian phone numbers in various formats:

- `05xxxxxxxx` (10 digits with leading 0)
- `5xxxxxxxx` (9 digits without leading 0)
- `+9665xxxxxxxx` (international format)
- `9665xxxxxxxx` (international without +)
- `009665xxxxxxxx` (international with 00)

All numbers are converted to the format `9665xxxxxxxx` for the SMS gateway.

## API Endpoints

### 1. Login with Phone (Existing - Enhanced)

**Endpoint**: `POST /api/client/login`

**Request**:
```json
{
    "phone": "**********"
}
```

**Response**:
```json
{
    "success": true,
    "message": "OTP sent successfully",
    "data": {
        "user_id": 123,
        "phone": "**********",
        "otp_expires_at": "2024-01-01T12:10:00Z"
    }
}
```

### 2. Resend OTP (Enhanced)

**Endpoint**: `POST /api/client/resend_otp`

**Request**:
```json
{
    "phone": "**********"
}
```

**Response**:
```json
{
    "success": true,
    "message": "OTP sent successfully"
}
```

### 3. Test SMS (New)

**Endpoint**: `POST /api/client/test_sms`

**Request**:
```json
{
    "phone": "**********",
    "message": "Test message (optional)"
}
```

**Response**:
```json
{
    "success": true,
    "message": "SMS sent successfully",
    "data": {
        "phone": "**********",
        "message": "Test message",
        "response": {
            "code": "M0000",
            "message": "Success"
        }
    }
}
```

## Testing

### 1. Test SMS Functionality

```bash
curl -X POST http://your-domain.com/api/client/test_sms \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "message": "Test SMS from Gather Point"
  }'
```

### 2. Test OTP Login Flow

```bash
# Step 1: Request OTP
curl -X POST http://your-domain.com/api/client/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********"
  }'

# Step 2: Validate OTP (use the OTP received via SMS)
curl -X POST http://your-domain.com/api/client/validate_otp \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "otp": "123456"
  }'
```

### 3. Test Resend OTP

```bash
curl -X POST http://your-domain.com/api/client/resend_otp \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********"
  }'
```

## Error Handling

### Common Error Responses

1. **Invalid Phone Number**:
```json
{
    "success": false,
    "message": "Invalid Saudi phone number format",
    "errors": {
        "phone": ["The phone field format is invalid."]
    }
}
```

2. **SMS Gateway Error**:
```json
{
    "success": false,
    "message": "Failed to send SMS: Gateway error",
    "data": {
        "error": "Insufficient balance"
    }
}
```

3. **Service Exception**:
```json
{
    "success": false,
    "message": "SMS service error: Connection timeout"
}
```

## Logging

All SMS operations are logged for debugging and monitoring:

- **Success logs**: Include phone number, OTP, and gateway response
- **Error logs**: Include error details and phone number
- **Log location**: `storage/logs/laravel.log`

## Security Considerations

1. **OTP Expiration**: OTPs expire after 10 minutes
2. **Rate Limiting**: Consider implementing rate limiting for OTP requests
3. **Phone Validation**: All phone numbers are validated before sending SMS
4. **Logging**: Sensitive information (OTP codes) are logged for debugging but should be removed in production

## Msegat Gateway Response Codes

- `M0000`: Success
- `M0001`: Invalid username or password
- `M0002`: Insufficient balance
- `M0003`: Invalid phone number
- `M0004`: Message too long
- `M0005`: Invalid sender name

## Monitoring

### Check Account Balance

The service includes a method to check your Msegat account balance:

```php
$smsService = new MsegatSmsService();
$balance = $smsService->getBalance();
```

### Check Delivery Status

You can check the delivery status of sent messages:

```php
$smsService = new MsegatSmsService();
$status = $smsService->getDeliveryStatus($messageId);
```

## Production Deployment

1. Update environment variables with production credentials
2. Remove or secure the test SMS endpoint
3. Implement proper rate limiting
4. Set up monitoring for SMS delivery failures
5. Configure log rotation for SMS logs
6. Test with real phone numbers before going live

## Support

For Msegat gateway support and documentation:
- API Documentation: https://msegat.docs.apiary.io
- Support: Contact Msegat support team
