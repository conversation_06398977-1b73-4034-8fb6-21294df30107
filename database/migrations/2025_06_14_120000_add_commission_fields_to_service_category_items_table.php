<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCommissionFieldsToServiceCategoryItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->boolean('include_commission_daily')->default(false)->comment('Include commission for daily pricing');
            $table->boolean('include_commission_weekly')->default(false)->comment('Include commission for weekly pricing');
            $table->boolean('include_commission_monthly')->default(false)->comment('Include commission for monthly pricing');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->dropColumn([
                'include_commission_daily',
                'include_commission_weekly', 
                'include_commission_monthly'
            ]);
        });
    }
};
