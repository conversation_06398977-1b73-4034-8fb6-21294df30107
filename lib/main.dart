import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'firebase_options.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:gather_point/core/managers/settings_cubit/settings_cubit.dart';
import 'package:gather_point/core/managers/app_initialization_cubit/app_initialization_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/services/fcm_service.dart';
import 'package:gather_point/core/managers/connectivity_cubit/connectivity_cubit.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:oktoast/oktoast.dart';

import 'core/routing/app_router.dart';
import 'core/theme/app_themes.dart';
import 'core/theme/theme_cubit.dart';
import 'core/utils/sound_manager.dart';
import 'feature/auth/Domain/Use Cases/login_use_case.dart';
import 'feature/auth/presentation/Manager/login_cubit/login_cubit.dart';

final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // --- Firebase Initialization ---
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Set up FCM background handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // Initialize FCM Service
  await FCMService.initialize();

  // Register Hive adapters
  Hive.registerAdapter(UserEntityAdapter());
  await Hive.initFlutter();
  await Hive.openBox<String>(AppConstants.kLanguageBoxName);
  await Hive.openBox<UserEntity>(AppConstants.kMyProfileBoxName);
  await Hive.openBox<bool>(AppConstants.kSettingsBoxName);

  // Open the settings box only once
  final settingsBox = await Hive.openBox<bool>(AppConstants.kSettingsBoxName);

  // Initialize isHosterModeNotifier after opening the box
  final isHosterModeNotifier = ValueNotifier<bool>(
    Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
            .get(AppConstants.kMyProfileKey)
            ?.isHosterMode ??
        false,
  );

  setupServiceLocator();
  AppRouter.setupRouter(); // Remove hasViewedOnboarding parameter

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => LocaleCubit()),
        BlocProvider(create: (context) => ConnectivityCubit()),
        BlocProvider(create: (context) => ThemeCubit()),
        BlocProvider(create: (context) => SettingsCubit(getIt<Box<bool>>())),
        BlocProvider(
          create: (context) => LoginCubit(
              loginUseCase: getIt<LoginUseCase>(),
              loginTokenUseCase: getIt<LoginTokenUseCase>(),
              loginGuestUseCase: getIt<LoginGuestUseCase>()),
        ),
        BlocProvider(
          create: (context) => AppInitializationCubit(
              loginGuestUseCase: getIt<LoginGuestUseCase>(),
              loginTokenUseCase: getIt<LoginTokenUseCase>()),
        ),
      ],
      child: GatherPointApp(
        isHosterModeNotifier: isHosterModeNotifier,
      ),
    ),
  );
}

class GatherPointApp extends StatelessWidget {
  final ValueNotifier<bool> isHosterModeNotifier;

  const GatherPointApp({super.key, required this.isHosterModeNotifier});

  @override
  Widget build(BuildContext context) {
    SoundManager.initialize(context);
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
          FocusManager.instance.primaryFocus?.unfocus();
        }
      },
      child: BlocListener<ConnectivityCubit, ConnectivityStatus>(
        listener: (context, state) {
          if (state == ConnectivityStatus.disconnected) {
            GoRouter.of(parentKey.currentContext!)
                .push(RoutesKeys.kNoInternetView);
          } else {
            if (GoRouter.of(parentKey.currentContext!).canPop()) {
              GoRouter.of(parentKey.currentContext!).pop();
            }
          }
        },
        child: ValueListenableBuilder<bool>(
          valueListenable: isHosterModeNotifier,
          builder: (context, isHosterMode, child) {
            return BlocBuilder<LocaleCubit, LocaleState>(
              buildWhen: (previousState, currentState) =>
                  previousState != currentState,
              builder: (_, localeState) {
                return OKToast(
                  child: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    routerConfig: AppRouter.router,
                    locale: localeState.locale,
                    localizationsDelegates: const [
                      S.delegate,
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate,
                    ],
                    supportedLocales: S.delegate.supportedLocales,
                    theme: lightTheme,
                    darkTheme: darkTheme,
                    themeMode:
                        context.watch<ThemeCubit>().state == AppThemeMode.light
                            ? ThemeMode.light
                            : ThemeMode.dark,
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
