import 'package:dio/dio.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:hive/hive.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'api_consumer.dart';
import 'end_points.dart';

class DioConsumer extends ApiConsumer {
  final Dio dio;
  final Box<UserEntity> profileBox; // Add this line

  DioConsumer({required this.dio, required this.profileBox}) {
    dio.options.baseUrl = EndPoints.baserUrl;
    dio.options.headers['Accept'] = 'application/json';
    dio.options.headers['Content-Type'] = 'application/json';
    dio.options.headers['accept-language'] = 'ar';
    if (Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
            .get(AppConstants.kMyProfileKey) !=
        null) {
      dio.options.headers['Authorization'] =
          'Bearer ${Hive.box<UserEntity>(AppConstants.kMyProfileBoxName).get(AppConstants.kMyProfileKey)!.token}';
    }
    dio.options.headers['X-localization'] =
        Hive.box<String>(AppConstants.kLanguageBoxName).get(
          AppConstants.kLanguageKey,
          defaultValue: AppConstants.kArabicLanguageCode,
        );
    dio.options.followRedirects = false;
    dio.interceptors.addAll([
      // ChuckerDioInterceptor(),
      PrettyDioLogger(
        requestBody: true,
        responseBody: true,
        enabled: true,
        requestHeader: true,
        request: true,
      )
    ]);
  }

  // Method to update the localization header
  void updateLocalization() {
    dio.options.headers['X-localization'] =
        Hive.box<String>(AppConstants.kLanguageBoxName).get(
          AppConstants.kLanguageKey,
          defaultValue: AppConstants.kArabicLanguageCode,
        );
  }

//!POST
  @override
  Future post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool isFormData = false,
  }) async {
    var response = await dio.post(
      path,
      data: isFormData && data is Map<String, dynamic>
          ? FormData.fromMap(data)
          : data,
      queryParameters: queryParameters,
    );
    return response.data;
  }

//!GET
  @override
  Future get(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    var res = await dio.get(
      path,
      data: data,
      queryParameters: queryParameters,
    );
    return res.data;
  }

//!DELETE
  @override
  Future delete(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    var res = await dio.delete(
      path,
      data: data,
      queryParameters: queryParameters,
    );
    return res.data;
  }

//!PATCH
  @override
  Future patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool isFormData = true,
  }) async {
    var res = await dio.patch(
      path,
      data: isFormData ? FormData.fromMap(data) : data,
      queryParameters: queryParameters,
    );
    return res.data;
  }

  @override
  Future put(
    String path, {
    data,
    Map<String, dynamic>? queryParameters,
    bool isFormData = true,
  }) async {
    var response = await dio.put(
      path,
      data: isFormData ? FormData.fromMap(data) : data,
      queryParameters: queryParameters,
    );
    return response.data;
  }
}
