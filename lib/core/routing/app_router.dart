import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'routes.dart';

final GlobalKey<NavigatorState> parentKey = GlobalKey<NavigatorState>();
final GlobalKey<NavigatorState> shellKey = GlobalKey<NavigatorState>();

extension GoRouterExtension on GoRouter {
  // Navigate back to a specific route
  void popUntilPath(BuildContext context, String ancestorPath) {
    while (routerDelegate.currentConfiguration.matches.last.matchedLocation !=
        ancestorPath) {
      if (!context.canPop()) {
        return;
      }
      context.pop();
    }
  }

  static dynamic back([dynamic popValue]) {
    return parentKey.currentState?.pop(popValue);
  }
}

abstract class AppRouter {
  static late GoRouter router;

  static void setupRouter() {
    router = GoRouter(
      navigatorKey: parentKey,
      routes: appRoutes,
      initialLocation: RoutesKeys.kSplashView, // Always start with splash
      errorBuilder: (context, state) {
        // Handle route errors gracefully
        return Scaffold(
          appBar: AppBar(title: const Text('خطأ في التنقل')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('لا يمكن العثور على الصفحة: ${state.matchedLocation}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.go(RoutesKeys.kHomeViewTab),
                  child: const Text('العودة للرئيسية'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
