import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/host/data/models/host_dashboard_model.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/host/data/services/host_api_service.dart';

part 'host_dashboard_state.dart';

class HostDashboardCubit extends Cubit<HostDashboardState> {
  final HostApiService _hostApiService;

  HostDashboardCubit(this._hostApiService) : super(HostDashboardInitial());

  /// Load host dashboard data
  Future<void> loadDashboardData() async {
    emit(HostDashboardLoading());
    
    try {
      final dashboardData = await _hostApiService.getHostDashboard();
      emit(HostDashboardLoaded(dashboardData));
    } catch (e) {
      emit(HostDashboardError(e.toString()));
    }
  }

  /// Load host reservations
  Future<void> loadHostReservations({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    emit(HostReservationsLoading());
    
    try {
      final reservations = await _hostApiService.getHostReservations(
        page: page,
        limit: limit,
        status: status,
      );
      emit(HostReservationsLoaded(reservations));
    } catch (e) {
      emit(HostReservationsError(e.toString()));
    }
  }

  /// Load host properties
  Future<void> loadHostProperties({
    int page = 1,
    int limit = 10,
  }) async {
    emit(HostPropertiesLoading());
    
    try {
      final properties = await _hostApiService.getHostProperties(
        page: page,
        limit: limit,
      );
      emit(HostPropertiesLoaded(properties));
    } catch (e) {
      emit(HostPropertiesError(e.toString()));
    }
  }

  /// Load financial summary
  Future<void> loadFinancialSummary() async {
    emit(HostFinancialLoading());
    
    try {
      final financialData = await _hostApiService.getFinancialSummary();
      emit(HostFinancialLoaded(financialData));
    } catch (e) {
      emit(HostFinancialError(e.toString()));
    }
  }

  /// Refresh all dashboard data
  Future<void> refreshDashboard() async {
    await loadDashboardData();
  }

  /// Create new property
  Future<void> createProperty(Map<String, dynamic> propertyData) async {
    emit(HostPropertyCreating());
    
    try {
      final newProperty = await _hostApiService.createProperty(propertyData);
      emit(HostPropertyCreated(newProperty));
      // Reload properties after creation
      loadHostProperties();
    } catch (e) {
      emit(HostPropertyCreationError(e.toString()));
    }
  }

  /// Update property
  Future<void> updateProperty(int propertyId, Map<String, dynamic> propertyData) async {
    emit(HostPropertyUpdating());
    
    try {
      final updatedProperty = await _hostApiService.updateProperty(propertyId, propertyData);
      emit(HostPropertyUpdated(updatedProperty));
      // Reload properties after update
      loadHostProperties();
    } catch (e) {
      emit(HostPropertyUpdateError(e.toString()));
    }
  }

  /// Upload property gallery
  Future<void> uploadPropertyGallery(int propertyId, List<String> imagePaths) async {
    emit(HostGalleryUploading());
    
    try {
      final success = await _hostApiService.uploadPropertyGallery(propertyId, imagePaths);
      if (success) {
        emit(HostGalleryUploaded());
        // Reload properties after gallery upload
        loadHostProperties();
      } else {
        emit(HostGalleryUploadError('Failed to upload gallery'));
      }
    } catch (e) {
      emit(HostGalleryUploadError(e.toString()));
    }
  }
}
