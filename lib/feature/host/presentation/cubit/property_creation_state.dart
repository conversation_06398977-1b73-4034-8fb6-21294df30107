part of 'property_creation_cubit.dart';

abstract class PropertyCreationState {}

class PropertyCreationInitial extends PropertyCreationState {}

class PropertyCreationLoading extends PropertyCreationState {}

class PropertyCreationDataLoaded extends PropertyCreationState {
  final List<ServiceCategory> categories;
  final List<FacilityModel> facilities;

  PropertyCreationDataLoaded({
    required this.categories,
    required this.facilities,
  });
}

class PropertyCreationSuccess extends PropertyCreationState {
  final PropertyItemModel property;

  PropertyCreationSuccess(this.property);
}

class PropertyCreationError extends PropertyCreationState {
  final String message;

  PropertyCreationError(this.message);
}

class PropertyGalleryUploaded extends PropertyCreationState {}
