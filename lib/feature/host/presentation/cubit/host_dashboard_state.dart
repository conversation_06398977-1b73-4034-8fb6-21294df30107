part of 'host_dashboard_cubit.dart';

abstract class HostDashboardState {}

class HostDashboardInitial extends HostDashboardState {}

// Dashboard States
class HostDashboardLoading extends HostDashboardState {}

class HostDashboardLoaded extends HostDashboardState {
  final HostDashboardModel dashboardData;

  HostDashboardLoaded(this.dashboardData);
}

class HostDashboardError extends HostDashboardState {
  final String message;

  HostDashboardError(this.message);
}

// Reservations States
class HostReservationsLoading extends HostDashboardState {}

class HostReservationsLoaded extends HostDashboardState {
  final List<ReservationModel> reservations;

  HostReservationsLoaded(this.reservations);
}

class HostReservationsError extends HostDashboardState {
  final String message;

  HostReservationsError(this.message);
}

// Properties States
class HostPropertiesLoading extends HostDashboardState {}

class HostPropertiesLoaded extends HostDashboardState {
  final List<PropertyItemModel> properties;

  HostPropertiesLoaded(this.properties);
}

class HostPropertiesError extends HostDashboardState {
  final String message;

  HostPropertiesError(this.message);
}

// Financial States
class HostFinancialLoading extends HostDashboardState {}

class HostFinancialLoaded extends HostDashboardState {
  final HostFinancialData financialData;

  HostFinancialLoaded(this.financialData);
}

class HostFinancialError extends HostDashboardState {
  final String message;

  HostFinancialError(this.message);
}

// Property Creation States
class HostPropertyCreating extends HostDashboardState {}

class HostPropertyCreated extends HostDashboardState {
  final PropertyItemModel property;

  HostPropertyCreated(this.property);
}

class HostPropertyCreationError extends HostDashboardState {
  final String message;

  HostPropertyCreationError(this.message);
}

// Property Update States
class HostPropertyUpdating extends HostDashboardState {}

class HostPropertyUpdated extends HostDashboardState {
  final PropertyItemModel property;

  HostPropertyUpdated(this.property);
}

class HostPropertyUpdateError extends HostDashboardState {
  final String message;

  HostPropertyUpdateError(this.message);
}

// Gallery Upload States
class HostGalleryUploading extends HostDashboardState {}

class HostGalleryUploaded extends HostDashboardState {}

class HostGalleryUploadError extends HostDashboardState {
  final String message;

  HostGalleryUploadError(this.message);
}
