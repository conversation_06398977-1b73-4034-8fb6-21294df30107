import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';

part 'property_creation_state.dart';

class PropertyCreationCubit extends Cubit<PropertyCreationState> {
  final PropertiesApiService _propertiesApiService;

  PropertyCreationCubit(this._propertiesApiService) : super(PropertyCreationInitial());

  /// Load initial data (categories and facilities)
  Future<void> loadInitialData() async {
    emit(PropertyCreationLoading());
    
    try {
      // Load categories and facilities in parallel
      final results = await Future.wait([
        _propertiesApiService.getServiceCategories(),
        _propertiesApiService.getFacilities(),
      ]);
      
      final categories = results[0] as List<ServiceCategory>;
      final facilities = results[1] as List<FacilityModel>;
      
      emit(PropertyCreationDataLoaded(
        categories: categories,
        facilities: facilities,
      ));
    } catch (e) {
      emit(PropertyCreationError(e.toString()));
    }
  }

  /// Create a new property
  Future<void> createProperty({
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
  }) async {
    emit(PropertyCreationLoading());
    
    try {
      final property = await _propertiesApiService.createProperty(
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        facilityIds: propertyData['facility_ids'],
        mainImage: mainImage,
        video: video,
        galleryImages: galleryImages,
      );
      
      emit(PropertyCreationSuccess(property));
    } catch (e) {
      emit(PropertyCreationError(e.toString()));
    }
  }

  /// Update an existing property
  Future<void> updateProperty({
    required int propertyId,
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
  }) async {
    emit(PropertyCreationLoading());
    
    try {
      final property = await _propertiesApiService.updateProperty(
        propertyId: propertyId,
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        facilityIds: propertyData['facility_ids'],
        mainImage: mainImage,
        video: video,
      );
      
      emit(PropertyCreationSuccess(property));
    } catch (e) {
      emit(PropertyCreationError(e.toString()));
    }
  }

  /// Upload gallery images for a property
  Future<void> uploadGalleryImages(int propertyId, List<File> images) async {
    emit(PropertyCreationLoading());
    
    try {
      final success = await _propertiesApiService.uploadGalleryImages(propertyId, images);
      
      if (success) {
        emit(PropertyGalleryUploaded());
      } else {
        emit(PropertyCreationError('Failed to upload gallery images'));
      }
    } catch (e) {
      emit(PropertyCreationError(e.toString()));
    }
  }
}
