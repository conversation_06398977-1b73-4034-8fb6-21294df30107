import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/feature/host/presentation/cubit/host_dashboard_cubit.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:intl/intl.dart';

class HostReservationsPage extends StatefulWidget {
  const HostReservationsPage({super.key});

  @override
  State<HostReservationsPage> createState() => _HostReservationsPageState();
}

class _HostReservationsPageState extends State<HostReservationsPage> {
  String _selectedFilter = 'all';
  final ReservationsApiService _reservationsApiService = getIt<ReservationsApiService>();

  @override
  void initState() {
    super.initState();
    _loadReservations();
  }

  void _loadReservations() {
    context.read<HostDashboardCubit>().loadHostReservations(
      status: _selectedFilter == 'all' ? null : _selectedFilter,
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: 'إدارة الحجوزات',
      hasBottomNavigation: false,
      body: Column(
        children: [
          // Filter Tabs
          _buildFilterTabs(s),
          
          // Reservations List
          Expanded(
            child: BlocBuilder<HostDashboardCubit, HostDashboardState>(
              builder: (context, state) {
                if (state is HostReservationsLoading) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                if (state is HostReservationsError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'خطأ في تحميل الحجوزات',
                          style: AppTextStyles.font16SemiBold.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: AppTextStyles.font14Regular.copyWith(
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadReservations,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }
                
                if (state is HostReservationsLoaded) {
                  final reservations = state.reservations;
                  
                  if (reservations.isEmpty) {
                    return _buildEmptyState(s);
                  }
                  
                  return RefreshIndicator(
                    onRefresh: () async => _loadReservations(),
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: reservations.length,
                      itemBuilder: (context, index) {
                        final reservation = reservations[index];
                        return _buildReservationCard(reservation, s);
                      },
                    ),
                  );
                }
                
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildFilterTab('all', 'الكل', _selectedFilter == 'all'),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterTab('pending', 'في الانتظار', _selectedFilter == 'pending'),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterTab('confirmed', 'مؤكدة', _selectedFilter == 'confirmed'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String value, String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = value;
        });
        _loadReservations();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.yellow : Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: AppTextStyles.font14SemiBold.copyWith(
            color: isSelected ? AppColors.black : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(S s) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.calendar_today_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد حجوزات',
            style: AppTextStyles.font18SemiBold.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا الحجوزات الخاصة بعقاراتك',
            style: AppTextStyles.font14Regular.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildReservationCard(ReservationModel reservation, S s) {
    final isConfirmed = reservation.confirmed;
    final statusColor = isConfirmed ? Colors.green : Colors.orange;
    final statusText = isConfirmed ? 'مؤكدة' : 'في الانتظار';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'حجز #${reservation.id}',
                  style: AppTextStyles.font16Bold,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    statusText,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Reservation details
            _buildDetailRow('العقار:', reservation.item?.title ?? 'غير محدد'),
            _buildDetailRow('العميل:', reservation.guestName ?? 'غير محدد'),
            _buildDetailRow(
              'من:',
              DateFormat('yyyy/MM/dd HH:mm').format(
                DateTime.parse(reservation.reservationFrom),
              ),
            ),
            _buildDetailRow(
              'إلى:',
              DateFormat('yyyy/MM/dd HH:mm').format(
                DateTime.parse(reservation.reservationTo),
              ),
            ),
            _buildDetailRow('المدة:', '${reservation.durationInDays} أيام'),
            
            const SizedBox(height: 16),
            
            // Action buttons for pending reservations
            if (!isConfirmed) _buildActionButtons(reservation),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.font14SemiBold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ReservationModel reservation) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () => _confirmReservation(reservation.id),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('قبول'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: () => _rejectReservation(reservation.id),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('رفض'),
          ),
        ),
      ],
    );
  }

  Future<void> _confirmReservation(int reservationId) async {
    try {
      await _reservationsApiService.confirmReservation(reservationId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم قبول الحجز بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadReservations();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في قبول الحجز: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _rejectReservation(int reservationId) async {
    try {
      await _reservationsApiService.cancelReservation(reservationId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفض الحجز'),
            backgroundColor: Colors.orange,
          ),
        );
        _loadReservations();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفض الحجز: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
