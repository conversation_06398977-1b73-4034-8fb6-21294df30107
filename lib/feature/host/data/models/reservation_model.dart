class ReservationModel {
  final int id;
  final String reservationDate;
  final String reservationFrom;
  final String reservationTo;
  final bool confirmed;
  final int serviceCategoryItemId;
  final int userId;
  final PropertyItemModel? item;
  final String createdAt;

  const ReservationModel({
    required this.id,
    required this.reservationDate,
    required this.reservationFrom,
    required this.reservationTo,
    required this.confirmed,
    required this.serviceCategoryItemId,
    required this.userId,
    this.item,
    required this.createdAt,
  });

  factory ReservationModel.fromJson(Map<String, dynamic> json) {
    return ReservationModel(
      id: json['id'] ?? 0,
      reservationDate: json['reservation_date'] ?? '',
      reservationFrom: json['reservation_from'] ?? '',
      reservationTo: json['reservation_to'] ?? '',
      confirmed: json['confirmed'] ?? false,
      serviceCategoryItemId: json['service_category_item_id'] ?? 0,
      userId: json['user_id'] ?? 0,
      item: json['item'] != null ? PropertyItemModel.fromJson(json['item']) : null,
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reservation_date': reservationDate,
      'reservation_from': reservationFrom,
      'reservation_to': reservationTo,
      'confirmed': confirmed,
      'service_category_item_id': serviceCategoryItemId,
      'user_id': userId,
      'item': item?.toJson(),
      'created_at': createdAt,
    };
  }

  // Helper methods
  String get status {
    if (confirmed) return 'confirmed';
    return 'pending';
  }

  int get durationInDays {
    try {
      final from = DateTime.parse(reservationFrom);
      final to = DateTime.parse(reservationTo);
      return to.difference(from).inDays;
    } catch (e) {
      return 1;
    }
  }

  double get totalAmount {
    if (item != null) {
      return (item!.price * durationInDays);
    }
    return 0.0;
  }
}

class PropertyItemModel {
  final int id;
  final String title;
  final String content;
  final String? image;
  final String? video;
  final double price;
  final double? weekendPrice;
  final double? weekPrice;
  final double? monthPrice;
  final double? lat;
  final double? lon;
  final bool active;
  final int serviceCategoryId;
  final int userId;
  final int views;
  final double rating;
  final int noOfRates;
  final int noGuests;
  final int beds;
  final int baths;
  final String? bookingRules;
  final String? cancelationRules;
  final String createdAt;

  const PropertyItemModel({
    required this.id,
    required this.title,
    required this.content,
    this.image,
    this.video,
    required this.price,
    this.weekendPrice,
    this.weekPrice,
    this.monthPrice,
    this.lat,
    this.lon,
    required this.active,
    required this.serviceCategoryId,
    required this.userId,
    required this.views,
    required this.rating,
    required this.noOfRates,
    required this.noGuests,
    required this.beds,
    required this.baths,
    this.bookingRules,
    this.cancelationRules,
    required this.createdAt,
  });

  factory PropertyItemModel.fromJson(Map<String, dynamic> json) {
    return PropertyItemModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      image: json['image'],
      video: json['video'],
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      weekendPrice: double.tryParse(json['weekend_price']?.toString() ?? '0'),
      weekPrice: double.tryParse(json['week_price']?.toString() ?? '0'),
      monthPrice: double.tryParse(json['month_price']?.toString() ?? '0'),
      lat: double.tryParse(json['lat']?.toString() ?? '0'),
      lon: double.tryParse(json['lon']?.toString() ?? '0'),
      active: json['active'] == 1 || json['active'] == true,
      serviceCategoryId: json['service_category_id'] ?? 0,
      userId: json['user_id'] ?? 0,
      views: json['views'] ?? 0,
      rating: double.tryParse(json['rating']?.toString() ?? '0') ?? 0.0,
      noOfRates: json['no_of_rates'] ?? 0,
      noGuests: json['no_guests'] ?? 1,
      beds: json['beds'] ?? 1,
      baths: json['baths'] ?? 1,
      bookingRules: json['booking_rules'],
      cancelationRules: json['cancelation_rules'],
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'image': image,
      'video': video,
      'price': price,
      'weekend_price': weekendPrice,
      'week_price': weekPrice,
      'month_price': monthPrice,
      'lat': lat,
      'lon': lon,
      'active': active,
      'service_category_id': serviceCategoryId,
      'user_id': userId,
      'views': views,
      'rating': rating,
      'no_of_rates': noOfRates,
      'no_guests': noGuests,
      'beds': beds,
      'baths': baths,
      'booking_rules': bookingRules,
      'cancelation_rules': cancelationRules,
      'created_at': createdAt,
    };
  }
}
