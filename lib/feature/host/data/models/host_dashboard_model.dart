class HostDashboardModel {
  final HostFinancialData financialData;
  final List<ReservationSummary> recentReservations;
  final List<ReviewSummary> recentReviews;
  final HostStatistics statistics;
  final List<EarningsData> earningsChart;
  final List<BookingsData> bookingsChart;

  const HostDashboardModel({
    required this.financialData,
    required this.recentReservations,
    required this.recentReviews,
    required this.statistics,
    required this.earningsChart,
    required this.bookingsChart,
  });

  factory HostDashboardModel.fromJson(Map<String, dynamic> json) {
    return HostDashboardModel(
      financialData: HostFinancialData.fromJson(json['financial_data'] ?? {}),
      recentReservations: (json['recent_reservations'] as List<dynamic>?)
              ?.map((item) => ReservationSummary.fromJson(item))
              .toList() ??
          [],
      recentReviews: (json['recent_reviews'] as List<dynamic>?)
              ?.map((item) => ReviewSummary.fromJson(item))
              .toList() ??
          [],
      statistics: HostStatistics.fromJson(json['statistics'] ?? {}),
      earningsChart: (json['earnings_chart'] as List<dynamic>?)
              ?.map((item) => EarningsData.fromJson(item))
              .toList() ??
          [],
      bookingsChart: (json['bookings_chart'] as List<dynamic>?)
              ?.map((item) => BookingsData.fromJson(item))
              .toList() ??
          [],
    );
  }
}

class HostFinancialData {
  final double walletBalance;
  final double totalEarnings;
  final double pendingEarnings;
  final double totalWithdrawn;
  final double thisMonthEarnings;

  const HostFinancialData({
    required this.walletBalance,
    required this.totalEarnings,
    required this.pendingEarnings,
    required this.totalWithdrawn,
    required this.thisMonthEarnings,
  });

  factory HostFinancialData.fromJson(Map<String, dynamic> json) {
    return HostFinancialData(
      walletBalance: double.tryParse(json['wallet_balance']?.toString() ?? '0') ?? 0.0,
      totalEarnings: double.tryParse(json['total_earnings']?.toString() ?? '0') ?? 0.0,
      pendingEarnings: double.tryParse(json['pending_earnings']?.toString() ?? '0') ?? 0.0,
      totalWithdrawn: double.tryParse(json['total_withdrawn']?.toString() ?? '0') ?? 0.0,
      thisMonthEarnings: double.tryParse(json['this_month_earnings']?.toString() ?? '0') ?? 0.0,
    );
  }
}

class ReservationSummary {
  final int id;
  final String guestName;
  final String propertyName;
  final String checkIn;
  final String checkOut;
  final int nights;
  final double amount;
  final String status;

  const ReservationSummary({
    required this.id,
    required this.guestName,
    required this.propertyName,
    required this.checkIn,
    required this.checkOut,
    required this.nights,
    required this.amount,
    required this.status,
  });

  factory ReservationSummary.fromJson(Map<String, dynamic> json) {
    return ReservationSummary(
      id: json['id'] ?? 0,
      guestName: json['guest_name'] ?? '',
      propertyName: json['property_name'] ?? '',
      checkIn: json['check_in'] ?? '',
      checkOut: json['check_out'] ?? '',
      nights: json['nights'] ?? 1,
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      status: json['status'] ?? 'pending',
    );
  }
}

class ReviewSummary {
  final int id;
  final String guestName;
  final String propertyName;
  final double rating;
  final String comment;
  final String date;

  const ReviewSummary({
    required this.id,
    required this.guestName,
    required this.propertyName,
    required this.rating,
    required this.comment,
    required this.date,
  });

  factory ReviewSummary.fromJson(Map<String, dynamic> json) {
    return ReviewSummary(
      id: json['id'] ?? 0,
      guestName: json['guest_name'] ?? '',
      propertyName: json['property_name'] ?? '',
      rating: double.tryParse(json['rating']?.toString() ?? '0') ?? 0.0,
      comment: json['comment'] ?? '',
      date: json['date'] ?? '',
    );
  }
}

class HostStatistics {
  final int totalProperties;
  final int totalReservations;
  final int totalReviews;
  final double averageRating;
  final int totalViews;

  const HostStatistics({
    required this.totalProperties,
    required this.totalReservations,
    required this.totalReviews,
    required this.averageRating,
    required this.totalViews,
  });

  factory HostStatistics.fromJson(Map<String, dynamic> json) {
    return HostStatistics(
      totalProperties: json['total_properties'] ?? 0,
      totalReservations: json['total_reservations'] ?? 0,
      totalReviews: json['total_reviews'] ?? 0,
      averageRating: double.tryParse(json['average_rating']?.toString() ?? '0') ?? 0.0,
      totalViews: json['total_views'] ?? 0,
    );
  }
}

class EarningsData {
  final String month;
  final double amount;
  final int monthNumber;

  const EarningsData({
    required this.month,
    required this.amount,
    required this.monthNumber,
  });

  factory EarningsData.fromJson(Map<String, dynamic> json) {
    return EarningsData(
      month: json['month'] ?? '',
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      monthNumber: json['month_number'] ?? 1,
    );
  }
}

class BookingsData {
  final String month;
  final int count;
  final int monthNumber;

  const BookingsData({
    required this.month,
    required this.count,
    required this.monthNumber,
  });

  factory BookingsData.fromJson(Map<String, dynamic> json) {
    return BookingsData(
      month: json['month'] ?? '',
      count: json['count'] ?? 0,
      monthNumber: json['month_number'] ?? 1,
    );
  }
}
