class WithdrawalModel {
  final int id;
  final double amount;
  final String method;
  final String status;
  final DateTime requestedAt;
  final DateTime? processedAt;
  final double fees;
  final double netAmount;
  final String reference;
  final String? notes;
  final Map<String, dynamic>? methodDetails;

  const WithdrawalModel({
    required this.id,
    required this.amount,
    required this.method,
    required this.status,
    required this.requestedAt,
    this.processedAt,
    required this.fees,
    required this.netAmount,
    required this.reference,
    this.notes,
    this.methodDetails,
  });

  factory WithdrawalModel.fromJson(Map<String, dynamic> json) {
    return WithdrawalModel(
      id: json['id'] ?? 0,
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      method: json['method'] ?? '',
      status: json['status'] ?? 'pending',
      requestedAt: DateTime.tryParse(json['requested_at'] ?? '') ?? DateTime.now(),
      processedAt: json['processed_at'] != null 
          ? DateTime.tryParse(json['processed_at']) 
          : null,
      fees: double.tryParse(json['fees']?.toString() ?? '0') ?? 0.0,
      netAmount: double.tryParse(json['net_amount']?.toString() ?? '0') ?? 0.0,
      reference: json['reference'] ?? '',
      notes: json['notes'],
      methodDetails: json['method_details'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'method': method,
      'status': status,
      'requested_at': requestedAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'fees': fees,
      'net_amount': netAmount,
      'reference': reference,
      'notes': notes,
      'method_details': methodDetails,
    };
  }

  // Helper methods
  String get statusText {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'processing':
        return 'قيد المعالجة';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'failed':
        return 'فشل';
      default:
        return status;
    }
  }

  String get methodText {
    switch (method.toLowerCase()) {
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'paypal':
        return 'PayPal';
      case 'wallet':
        return 'محفظة إلكترونية';
      default:
        return method;
    }
  }

  bool get canBeCancelled {
    return status.toLowerCase() == 'pending';
  }

  bool get isCompleted {
    return status.toLowerCase() == 'completed';
  }

  bool get isPending {
    return status.toLowerCase() == 'pending';
  }

  bool get isProcessing {
    return status.toLowerCase() == 'processing';
  }

  bool get isFailed {
    return status.toLowerCase() == 'failed';
  }

  bool get isCancelled {
    return status.toLowerCase() == 'cancelled';
  }

  String get formattedAmount {
    return '\$${amount.toStringAsFixed(2)}';
  }

  String get formattedNetAmount {
    return '\$${netAmount.toStringAsFixed(2)}';
  }

  String get formattedFees {
    return '\$${fees.toStringAsFixed(2)}';
  }

  // Duration since request
  Duration get timeSinceRequest {
    return DateTime.now().difference(requestedAt);
  }

  String get timeSinceRequestText {
    final duration = timeSinceRequest;
    
    if (duration.inDays > 0) {
      return 'منذ ${duration.inDays} ${duration.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (duration.inHours > 0) {
      return 'منذ ${duration.inHours} ${duration.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (duration.inMinutes > 0) {
      return 'منذ ${duration.inMinutes} ${duration.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  WithdrawalModel copyWith({
    int? id,
    double? amount,
    String? method,
    String? status,
    DateTime? requestedAt,
    DateTime? processedAt,
    double? fees,
    double? netAmount,
    String? reference,
    String? notes,
    Map<String, dynamic>? methodDetails,
  }) {
    return WithdrawalModel(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      status: status ?? this.status,
      requestedAt: requestedAt ?? this.requestedAt,
      processedAt: processedAt ?? this.processedAt,
      fees: fees ?? this.fees,
      netAmount: netAmount ?? this.netAmount,
      reference: reference ?? this.reference,
      notes: notes ?? this.notes,
      methodDetails: methodDetails ?? this.methodDetails,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is WithdrawalModel &&
        other.id == id &&
        other.amount == amount &&
        other.method == method &&
        other.status == status &&
        other.requestedAt == requestedAt &&
        other.processedAt == processedAt &&
        other.fees == fees &&
        other.netAmount == netAmount &&
        other.reference == reference &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      amount,
      method,
      status,
      requestedAt,
      processedAt,
      fees,
      netAmount,
      reference,
      notes,
    );
  }

  @override
  String toString() {
    return 'WithdrawalModel(id: $id, amount: $amount, method: $method, status: $status, reference: $reference)';
  }
}
