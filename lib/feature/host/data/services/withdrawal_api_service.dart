import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/host/data/models/withdrawal_model.dart';

class WithdrawalApiService {
  final DioConsumer _dioConsumer;

  WithdrawalApiService(this._dioConsumer);

  /// Request a withdrawal
  Future<WithdrawalModel> requestWithdrawal({
    required double amount,
    required String method,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final data = {
        'amount': amount,
        'method': method,
        ...?additionalData,
      };

      final response = await _dioConsumer.post(
        '/api/host/withdrawal/request',
        data: data,
      );

      if (response['success'] == true) {
        return WithdrawalModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to request withdrawal');
      }
    } catch (e) {
      throw Exception('Failed to request withdrawal: ${e.toString()}');
    }
  }

  /// Get withdrawal history
  Future<List<WithdrawalModel>> getWithdrawalHistory({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
      };

      final response = await _dioConsumer.get(
        '/api/host/withdrawal/history',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => WithdrawalModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch withdrawal history');
      }
    } catch (e) {
      // Return dummy data for now
      return _getDummyWithdrawals();
    }
  }

  /// Get withdrawal methods
  Future<List<WithdrawalMethod>> getWithdrawalMethods() async {
    try {
      final response = await _dioConsumer.get('/api/host/withdrawal/methods');

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => WithdrawalMethod.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch withdrawal methods');
      }
    } catch (e) {
      // Return default methods
      return [
        const WithdrawalMethod(
          id: 'bank_transfer',
          name: 'تحويل بنكي',
          icon: 'bank',
          minAmount: 100.0,
          maxAmount: 10000.0,
          processingTime: '1-3 أيام عمل',
          fees: 0.0,
        ),
        const WithdrawalMethod(
          id: 'paypal',
          name: 'PayPal',
          icon: 'paypal',
          minAmount: 50.0,
          maxAmount: 5000.0,
          processingTime: '24 ساعة',
          fees: 2.5,
        ),
      ];
    }
  }

  /// Cancel a withdrawal request
  Future<bool> cancelWithdrawal(int withdrawalId) async {
    try {
      final response = await _dioConsumer.delete(
        '/api/host/withdrawal/cancel/$withdrawalId',
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to cancel withdrawal: ${e.toString()}');
    }
  }

  // Dummy data for development
  List<WithdrawalModel> _getDummyWithdrawals() {
    return [
      WithdrawalModel(
        id: 1,
        amount: 500.0,
        method: 'bank_transfer',
        status: 'completed',
        requestedAt: DateTime.now().subtract(const Duration(days: 5)),
        processedAt: DateTime.now().subtract(const Duration(days: 2)),
        fees: 0.0,
        netAmount: 500.0,
        reference: 'WD001',
      ),
      WithdrawalModel(
        id: 2,
        amount: 250.0,
        method: 'paypal',
        status: 'processing',
        requestedAt: DateTime.now().subtract(const Duration(days: 1)),
        processedAt: null,
        fees: 6.25,
        netAmount: 243.75,
        reference: 'WD002',
      ),
      WithdrawalModel(
        id: 3,
        amount: 1000.0,
        method: 'bank_transfer',
        status: 'pending',
        requestedAt: DateTime.now().subtract(const Duration(hours: 2)),
        processedAt: null,
        fees: 0.0,
        netAmount: 1000.0,
        reference: 'WD003',
      ),
    ];
  }
}

class WithdrawalMethod {
  final String id;
  final String name;
  final String icon;
  final double minAmount;
  final double maxAmount;
  final String processingTime;
  final double fees; // Percentage or fixed amount

  const WithdrawalMethod({
    required this.id,
    required this.name,
    required this.icon,
    required this.minAmount,
    required this.maxAmount,
    required this.processingTime,
    required this.fees,
  });

  factory WithdrawalMethod.fromJson(Map<String, dynamic> json) {
    return WithdrawalMethod(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
      minAmount: double.tryParse(json['min_amount']?.toString() ?? '0') ?? 0.0,
      maxAmount: double.tryParse(json['max_amount']?.toString() ?? '0') ?? 0.0,
      processingTime: json['processing_time'] ?? '',
      fees: double.tryParse(json['fees']?.toString() ?? '0') ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'min_amount': minAmount,
      'max_amount': maxAmount,
      'processing_time': processingTime,
      'fees': fees,
    };
  }
}
