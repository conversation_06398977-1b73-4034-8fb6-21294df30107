import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/auth/presentation/Manager/validate_otp_cubit/validate_otp_cubit.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:pinput/pinput.dart';

class EnterOTPSection extends StatelessWidget {
  const EnterOTPSection({super.key, required this.formKey});

  final GlobalKey<FormState> formKey;

  @override
  Widget build(BuildContext context) {
    PinTheme pinTheme = PinTheme(
      width: 66,
      height: 57,
      textStyle: AppTextStyles.font30SemiBold.copyWith(color: AppColors.black),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppColors.white,
      ),
    );

    return Form(
      key: form<PERSON><PERSON>,
      child: Center(
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: Pinput(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            followingPinTheme: pinTheme,
            length: 4,
            onCompleted: (pin) {
              BlocProvider.of<ValidateOtpCubit>(context).otpCode = pin;
            },
            closeKeyboardWhenCompleted: true,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
            ],
            pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
            showCursor: true,
            keyboardType: TextInputType.number,
            defaultPinTheme: pinTheme,
            focusedPinTheme: pinTheme,
            validator: (value) {
              if (value == null || value.length < 4) {
                return S.of(context).enterVerificationCode;
              }
              return null;
            },
          ),
        ),
      ),
    );
  }
}
