import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/sound_enhanced_buttons.dart';
import 'package:gather_point/feature/auth/presentation/manager/login_cubit/login_cubit.dart';
import 'package:gather_point/generated/l10n.dart';

class SocialLoginButtons extends StatelessWidget {
  const SocialLoginButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Column(
      children: [
        // Google Sign-In Button
        _buildGoogleSignInButton(context, s),
        
        const SizedBox(height: 12),
        
        // Apple Sign-In Button (iOS only)
        if (Platform.isIOS) ...[
          _buildAppleSignInButton(context, s),
          const Sized<PERSON>ox(height: 12),
        ],
        
        // Divider with "OR" text
        _buildDivider(context, s),
      ],
    );
  }

  Widget _buildGoogleSignInButton(BuildContext context, S s) {
    return SoundElevatedButton(
      onPressed: () => _handleGoogleSignIn(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        shadowColor: Colors.black26,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
        minimumSize: const Size(double.infinity, 50),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Google Logo
          SvgPicture.asset(
            'assets/icons/google_logo.svg',
            width: 20,
            height: 20,
          ),
          const SizedBox(width: 12),
          Text(
            s.proceedWithGoogle,
            style: AppTextStyles.font16Medium.copyWith(
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppleSignInButton(BuildContext context, S s) {
    final isDark = context.isDarkMode;
    
    return SoundElevatedButton(
      onPressed: () => _handleAppleSignIn(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: isDark ? Colors.white : Colors.black,
        foregroundColor: isDark ? Colors.black : Colors.white,
        elevation: 1,
        shadowColor: Colors.black26,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
        minimumSize: const Size(double.infinity, 50),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Apple Logo
          Icon(
            Icons.apple,
            size: 20,
            color: isDark ? Colors.black : Colors.white,
          ),
          const SizedBox(width: 12),
          Text(
            s.proceedWithApple,
            style: AppTextStyles.font16Medium.copyWith(
              color: isDark ? Colors.black : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context, S s) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: context.secondaryTextColor.withValues(alpha: 0.3),
              thickness: 1,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              s.or,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ),
          Expanded(
            child: Divider(
              color: context.secondaryTextColor.withValues(alpha: 0.3),
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  void _handleGoogleSignIn(BuildContext context) async {
    final loginCubit = context.read<LoginCubit>();
    
    try {
      // Show loading state
      loginCubit.setLoading(true);
      
      // Trigger Google sign-in through the cubit
      await loginCubit.signInWithGoogle();
      
    } catch (error) {
      debugPrint('Google sign-in error: $error');
      
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google sign-in failed: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Hide loading state
      if (context.mounted) {
        loginCubit.setLoading(false);
      }
    }
  }

  void _handleAppleSignIn(BuildContext context) async {
    final loginCubit = context.read<LoginCubit>();
    
    try {
      // Show loading state
      loginCubit.setLoading(true);
      
      // Trigger Apple sign-in through the cubit
      await loginCubit.signInWithApple();
      
    } catch (error) {
      debugPrint('Apple sign-in error: $error');
      
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple sign-in failed: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Hide loading state
      if (context.mounted) {
        loginCubit.setLoading(false);
      }
    }
  }
}

/// Alternative compact social login buttons for smaller spaces
class CompactSocialLoginButtons extends StatelessWidget {
  const CompactSocialLoginButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Google Sign-In Button
        Expanded(
          child: _buildCompactGoogleButton(context),
        ),
        
        const SizedBox(width: 12),
        
        // Apple Sign-In Button (iOS only)
        if (Platform.isIOS)
          Expanded(
            child: _buildCompactAppleButton(context),
          ),
      ],
    );
  }

  Widget _buildCompactGoogleButton(BuildContext context) {
    return SoundElevatedButton(
      onPressed: () => _handleGoogleSignIn(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        shadowColor: Colors.black26,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12),
        minimumSize: const Size(0, 44),
      ),
      child: SvgPicture.asset(
        'assets/icons/google_logo.svg',
        width: 20,
        height: 20,
      ),
    );
  }

  Widget _buildCompactAppleButton(BuildContext context) {
    final isDark = context.isDarkMode;
    
    return SoundElevatedButton(
      onPressed: () => _handleAppleSignIn(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: isDark ? Colors.white : Colors.black,
        foregroundColor: isDark ? Colors.black : Colors.white,
        elevation: 1,
        shadowColor: Colors.black26,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12),
        minimumSize: const Size(0, 44),
      ),
      child: Icon(
        Icons.apple,
        size: 20,
        color: isDark ? Colors.black : Colors.white,
      ),
    );
  }

  void _handleGoogleSignIn(BuildContext context) async {
    final loginCubit = context.read<LoginCubit>();
    await loginCubit.signInWithGoogle();
  }

  void _handleAppleSignIn(BuildContext context) async {
    final loginCubit = context.read<LoginCubit>();
    await loginCubit.signInWithApple();
  }
}
