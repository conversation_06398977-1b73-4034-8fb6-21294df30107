import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/core/widgets/custom_error_toast.dart';
import 'package:gather_point/feature/auth/presentation/Manager/login_cubit/login_cubit.dart';
import 'package:gather_point/generated/l10n.dart';

class LoginMobileNumberInputField extends StatefulWidget {
  const LoginMobileNumberInputField({
    super.key,
  });

  @override
  State<LoginMobileNumberInputField> createState() =>
      _LoginMobileNumberInputFieldState();
}

class _LoginMobileNumberInputFieldState
    extends State<LoginMobileNumberInputField> {
  TextEditingController controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.black,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.yellow),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            const LoginMobileNumberInputField(),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller:
                    BlocProvider.of<LoginCubit>(context).phoneController,
                textAlign: TextAlign.end,
                style: AppTextStyles.font16Bold.copyWith(
                  color: AppColors.white,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                cursorColor: AppColors.yellow,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  hintText: S.of(context).enterPhoneNumber,
                  hintStyle: AppTextStyles.font16Bold.copyWith(
                    color: AppColors.white.withOpacity(0.6),
                  ),
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const VerticalDivider(
              color: AppColors.lightGrey8,
              thickness: 1,
              width: 1,
              indent: 12,
              endIndent: 12,
            ),
            const SizedBox(width: 8),
            Text(
              '966+',
              style: AppTextStyles.font16Bold.copyWith(
                color: AppColors.white.withOpacity(0.6),
              ),
            ),
            const SizedBox(width: 4),
            SvgPicture.asset(AppAssets.imagesSaudiArabia),
            const SizedBox(width: 8),
            PrimaryButton(
              fullWidth: false,
              onPressed: () {
                SoundManager.playClickSound();
                if (BlocProvider.of<LoginCubit>(context)
                    .phoneController
                    .text
                    .trim()
                    .isEmpty) {
                  showCustomErrorToast(S.of(context).pleaseEnterPhoneNumber);
                  return;
                } else if (!RegExp(
                        r'^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$')
                    .hasMatch(BlocProvider.of<LoginCubit>(context)
                        .phoneController
                        .text
                        .trim())) {
                  showCustomErrorToast(S.of(context).pleaseCheckPhoneNumber);
                  return;
                } else {
                  BlocProvider.of<LoginCubit>(context).login();
                }
              },
              label: S.of(context).login,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              borderRadius: 20,
            )
          ],
        ),
      ),
    );
  }
}
