import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/feature/auth/presentation/Manager/login_cubit/login_cubit.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/login_button_section.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/core/routing/routes_keys.dart';

class LoginViewBody extends StatelessWidget {
  const LoginViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return BlocListener<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state is LoginGuestSuccessState) {
          GoRouter.of(context).pushReplacement(RoutesKeys.kHomeViewTab);
        } else if (state is LoginSuccessState) {
          GoRouter.of(context).pushReplacement(RoutesKeys.kVerifyOTPView);
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            PrimaryButton(
              onPressed: () {
                context.read<LoginCubit>().loginGuest();
              },
              label: s.welcomeGuest,
            ),
            const SizedBox(height: 8),
            const LoginButtonSection(),
            const SizedBox(height: 24),
            //const LoginFooterSection(),
          ],
        ),
      ),
    );
  }
}
