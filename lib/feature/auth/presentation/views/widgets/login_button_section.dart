import 'package:flutter/material.dart';
import 'package:gather_point/core/components/secondary_button.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/login_mobile_number_input_field.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/social_login_buttons.dart';
import 'package:gather_point/generated/l10n.dart';

class LoginButtonSection extends StatefulWidget {
  const LoginButtonSection({super.key});

  @override
  State<LoginButtonSection> createState() => _LoginButtonSectionState();
}

class _LoginButtonSectionState extends State<LoginButtonSection> {
  bool showMobileNumberInput = false;
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return showMobileNumberInput
        ? const LoginMobileNumberInputField()
        : Column(
            children: [
              // Social Login Buttons
              const SocialLoginButtons(),

              const SizedBox(height: 16),

              // Phone Login Button
              SecondaryButton(
                onPressed: () {
                  SoundManager.playClickSound();
                  setState(() => showMobileNumberInput = !showMobileNumberInput);
                },
                label: s.proceedWithPhone,
                backgroundColor: AppColors.black,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ],
          );
  }
}
