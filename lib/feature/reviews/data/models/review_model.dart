class ReviewModel {
  final int id;
  final int userId;
  final int propertyId;
  final int reservationId;
  final double rating;
  final String comment;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserModel? user;
  final PropertyModel? property;
  final List<ReviewImageModel>? images;

  const ReviewModel({
    required this.id,
    required this.userId,
    required this.propertyId,
    required this.reservationId,
    required this.rating,
    required this.comment,
    required this.createdAt,
    required this.updatedAt,
    this.user,
    this.property,
    this.images,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      propertyId: json['property_id'] ?? 0,
      reservationId: json['reservation_id'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      comment: json['comment'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
      property: json['property'] != null ? PropertyModel.fromJson(json['property']) : null,
      images: json['images'] != null 
          ? (json['images'] as List).map((img) => ReviewImageModel.fromJson(img)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'property_id': propertyId,
      'reservation_id': reservationId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      if (user != null) 'user': user!.toJson(),
      if (property != null) 'property': property!.toJson(),
      if (images != null) 'images': images!.map((img) => img.toJson()).toList(),
    };
  }
}

class ReviewImageModel {
  final int id;
  final int reviewId;
  final String imageUrl;
  final DateTime createdAt;

  const ReviewImageModel({
    required this.id,
    required this.reviewId,
    required this.imageUrl,
    required this.createdAt,
  });

  factory ReviewImageModel.fromJson(Map<String, dynamic> json) {
    return ReviewImageModel(
      id: json['id'] ?? 0,
      reviewId: json['review_id'] ?? 0,
      imageUrl: json['image_url'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'review_id': reviewId,
      'image_url': imageUrl,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class UserModel {
  final int id;
  final String name;
  final String? email;
  final String? phone;
  final String? avatar;

  const UserModel({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.avatar,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'],
      avatar: json['avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
    };
  }
}

class PropertyModel {
  final int id;
  final String title;
  final String? image;

  const PropertyModel({
    required this.id,
    required this.title,
    this.image,
  });

  factory PropertyModel.fromJson(Map<String, dynamic> json) {
    return PropertyModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image': image,
    };
  }
}

class ReviewStatsModel {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution; // rating -> count

  const ReviewStatsModel({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
  });

  factory ReviewStatsModel.fromJson(Map<String, dynamic> json) {
    final distribution = <int, int>{};
    if (json['rating_distribution'] != null) {
      final dynamic dist = json['rating_distribution'];
      if (dist is Map) {
        dist.forEach((key, value) {
          distribution[int.parse(key.toString())] = value as int;
        });
      }
    }

    return ReviewStatsModel(
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      ratingDistribution: distribution,
    );
  }

  Map<String, dynamic> toJson() {
    final distribution = <String, int>{};
    ratingDistribution.forEach((key, value) {
      distribution[key.toString()] = value;
    });

    return {
      'average_rating': averageRating,
      'total_reviews': totalReviews,
      'rating_distribution': distribution,
    };
  }
}

class CreateReviewRequest {
  final int propertyId;
  final int reservationId;
  final double rating;
  final String comment;
  final List<String>? imagePaths;

  const CreateReviewRequest({
    required this.propertyId,
    required this.reservationId,
    required this.rating,
    required this.comment,
    this.imagePaths,
  });

  Map<String, dynamic> toJson() {
    return {
      'property_id': propertyId,
      'reservation_id': reservationId,
      'rating': rating,
      'comment': comment,
      if (imagePaths != null) 'image_paths': imagePaths,
    };
  }
}
