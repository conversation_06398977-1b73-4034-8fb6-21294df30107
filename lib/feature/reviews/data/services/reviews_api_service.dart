import 'dart:io';
import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/reviews/data/models/review_model.dart';

class ReviewsApiService {
  final DioConsumer _dioConsumer;

  ReviewsApiService(this._dioConsumer);

  /// Get reviews for a property
  Future<List<ReviewModel>> getPropertyReviews({
    required int propertyId,
    int page = 1,
    int limit = 10,
    double? minRating,
    String? sortBy = 'newest', // newest, oldest, highest_rated, lowest_rated
  }) async {
    try {
      final queryParams = {
        'property_id': propertyId,
        'page': page,
        'limit': limit,
        if (minRating != null) 'min_rating': minRating,
        'sort_by': sortBy,
      };

      final response = await _dioConsumer.get(
        '/api/reviews',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ReviewModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch reviews');
      }
    } catch (e) {
      throw Exception('Failed to fetch reviews: ${e.toString()}');
    }
  }

  /// Get review statistics for a property
  Future<ReviewStatsModel> getPropertyReviewStats(int propertyId) async {
    try {
      final response = await _dioConsumer.get('/api/reviews/stats/$propertyId');

      if (response['success'] == true) {
        return ReviewStatsModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch review stats');
      }
    } catch (e) {
      throw Exception('Failed to fetch review stats: ${e.toString()}');
    }
  }

  /// Create a new review
  Future<ReviewModel> createReview({
    required int propertyId,
    required int reservationId,
    required double rating,
    required String comment,
    List<File>? images,
  }) async {
    try {
      final formData = <String, dynamic>{
        'property_id': propertyId,
        'reservation_id': reservationId,
        'rating': rating,
        'comment': comment,
      };

      // Add images if provided
      if (images != null && images.isNotEmpty) {
        for (int i = 0; i < images.length; i++) {
          formData['images[$i]'] = await MultipartFile.fromFile(
            images[i].path,
            filename: images[i].path.split('/').last,
          );
        }
      }

      final response = await _dioConsumer.post(
        '/api/reviews',
        data: formData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return ReviewModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create review');
      }
    } catch (e) {
      throw Exception('Failed to create review: ${e.toString()}');
    }
  }

  /// Update an existing review
  Future<ReviewModel> updateReview({
    required int reviewId,
    double? rating,
    String? comment,
    List<File>? newImages,
    List<int>? removeImageIds,
  }) async {
    try {
      final formData = <String, dynamic>{};

      if (rating != null) formData['rating'] = rating;
      if (comment != null) formData['comment'] = comment;
      if (removeImageIds != null) formData['remove_image_ids'] = removeImageIds;

      // Add new images if provided
      if (newImages != null && newImages.isNotEmpty) {
        for (int i = 0; i < newImages.length; i++) {
          formData['new_images[$i]'] = await MultipartFile.fromFile(
            newImages[i].path,
            filename: newImages[i].path.split('/').last,
          );
        }
      }

      final response = await _dioConsumer.put(
        '/api/reviews/$reviewId',
        data: formData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return ReviewModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update review');
      }
    } catch (e) {
      throw Exception('Failed to update review: ${e.toString()}');
    }
  }

  /// Delete a review
  Future<bool> deleteReview(int reviewId) async {
    try {
      final response = await _dioConsumer.delete('/api/reviews/$reviewId');
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to delete review: ${e.toString()}');
    }
  }

  /// Get user's reviews
  Future<List<ReviewModel>> getUserReviews({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
      };

      final response = await _dioConsumer.get(
        '/api/reviews/user',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ReviewModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch user reviews');
      }
    } catch (e) {
      throw Exception('Failed to fetch user reviews: ${e.toString()}');
    }
  }

  /// Get reviews for host's properties
  Future<List<ReviewModel>> getHostReviews({
    int page = 1,
    int limit = 10,
    int? propertyId,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (propertyId != null) 'property_id': propertyId,
      };

      final response = await _dioConsumer.get(
        '/api/reviews/host',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ReviewModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch host reviews');
      }
    } catch (e) {
      throw Exception('Failed to fetch host reviews: ${e.toString()}');
    }
  }

  /// Report a review
  Future<bool> reportReview({
    required int reviewId,
    required String reason,
    String? description,
  }) async {
    try {
      final response = await _dioConsumer.post(
        '/api/reviews/$reviewId/report',
        data: {
          'reason': reason,
          if (description != null) 'description': description,
        },
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to report review: ${e.toString()}');
    }
  }

  /// Like/Unlike a review
  Future<bool> toggleReviewLike(int reviewId) async {
    try {
      final response = await _dioConsumer.post('/api/reviews/$reviewId/like');
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to toggle review like: ${e.toString()}');
    }
  }

  /// Check if user can review a property (has completed reservation)
  Future<bool> canReviewProperty({
    required int propertyId,
    required int reservationId,
  }) async {
    try {
      final response = await _dioConsumer.get(
        '/api/reviews/can-review',
        queryParameters: {
          'property_id': propertyId,
          'reservation_id': reservationId,
        },
      );

      return response['success'] == true && response['data']['can_review'] == true;
    } catch (e) {
      throw Exception('Failed to check review eligibility: ${e.toString()}');
    }
  }
}
