import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/reviews/data/models/review_model.dart';
import 'package:gather_point/feature/reviews/data/services/reviews_api_service.dart';

part 'review_state.dart';

class ReviewCubit extends Cubit<ReviewState> {
  final ReviewsApiService _reviewsApiService;

  ReviewCubit(this._reviewsApiService) : super(ReviewInitial());

  /// Get reviews for a property
  Future<void> getPropertyReviews({
    required int propertyId,
    int page = 1,
    int limit = 10,
    double? minRating,
    String? sortBy = 'newest',
  }) async {
    emit(ReviewLoading());
    
    try {
      final reviews = await _reviewsApiService.getPropertyReviews(
        propertyId: propertyId,
        page: page,
        limit: limit,
        minRating: minRating,
        sortBy: sortBy,
      );
      
      emit(ReviewsLoaded(reviews));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Get review statistics for a property
  Future<void> getPropertyReviewStats(int propertyId) async {
    emit(ReviewLoading());
    
    try {
      final stats = await _reviewsApiService.getPropertyReviewStats(propertyId);
      emit(ReviewStatsLoaded(stats));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Create a new review
  Future<void> createReview({
    required int propertyId,
    required int reservationId,
    required double rating,
    required String comment,
    List<File>? images,
  }) async {
    emit(ReviewLoading());
    
    try {
      final review = await _reviewsApiService.createReview(
        propertyId: propertyId,
        reservationId: reservationId,
        rating: rating,
        comment: comment,
        images: images,
      );
      
      emit(ReviewSuccess(review));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Update an existing review
  Future<void> updateReview({
    required int reviewId,
    double? rating,
    String? comment,
    List<File>? newImages,
    List<int>? removeImageIds,
  }) async {
    emit(ReviewLoading());
    
    try {
      final review = await _reviewsApiService.updateReview(
        reviewId: reviewId,
        rating: rating,
        comment: comment,
        newImages: newImages,
        removeImageIds: removeImageIds,
      );
      
      emit(ReviewSuccess(review));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Delete a review
  Future<void> deleteReview(int reviewId) async {
    emit(ReviewLoading());
    
    try {
      final success = await _reviewsApiService.deleteReview(reviewId);
      
      if (success) {
        emit(ReviewDeleted());
      } else {
        emit(ReviewError('Failed to delete review'));
      }
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Get user's reviews
  Future<void> getUserReviews({
    int page = 1,
    int limit = 10,
  }) async {
    emit(ReviewLoading());
    
    try {
      final reviews = await _reviewsApiService.getUserReviews(
        page: page,
        limit: limit,
      );
      
      emit(ReviewsLoaded(reviews));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Get reviews for host's properties
  Future<void> getHostReviews({
    int page = 1,
    int limit = 10,
    int? propertyId,
  }) async {
    emit(ReviewLoading());
    
    try {
      final reviews = await _reviewsApiService.getHostReviews(
        page: page,
        limit: limit,
        propertyId: propertyId,
      );
      
      emit(ReviewsLoaded(reviews));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Report a review
  Future<void> reportReview({
    required int reviewId,
    required String reason,
    String? description,
  }) async {
    emit(ReviewLoading());
    
    try {
      final success = await _reviewsApiService.reportReview(
        reviewId: reviewId,
        reason: reason,
        description: description,
      );
      
      if (success) {
        emit(ReviewReported());
      } else {
        emit(ReviewError('Failed to report review'));
      }
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Toggle review like
  Future<void> toggleReviewLike(int reviewId) async {
    try {
      await _reviewsApiService.toggleReviewLike(reviewId);
      // Emit a state to update UI if needed
      emit(ReviewLikeToggled());
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Check if user can review a property
  Future<void> checkCanReview({
    required int propertyId,
    required int reservationId,
  }) async {
    emit(ReviewLoading());
    
    try {
      final canReview = await _reviewsApiService.canReviewProperty(
        propertyId: propertyId,
        reservationId: reservationId,
      );
      
      emit(ReviewEligibilityChecked(canReview));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }

  /// Load reviews with stats combined
  Future<void> loadPropertyReviewsWithStats({
    required int propertyId,
    int page = 1,
    int limit = 10,
    double? minRating,
    String? sortBy = 'newest',
  }) async {
    emit(ReviewLoading());
    
    try {
      // Load both reviews and stats in parallel
      final results = await Future.wait([
        _reviewsApiService.getPropertyReviews(
          propertyId: propertyId,
          page: page,
          limit: limit,
          minRating: minRating,
          sortBy: sortBy,
        ),
        _reviewsApiService.getPropertyReviewStats(propertyId),
      ]);
      
      final reviews = results[0] as List<ReviewModel>;
      final stats = results[1] as ReviewStatsModel;
      
      emit(ReviewsWithStatsLoaded(reviews, stats));
    } catch (e) {
      emit(ReviewError(e.toString()));
    }
  }
}
