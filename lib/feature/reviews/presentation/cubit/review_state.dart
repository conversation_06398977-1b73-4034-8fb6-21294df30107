part of 'review_cubit.dart';

abstract class ReviewState {}

class ReviewInitial extends ReviewState {}

class ReviewLoading extends ReviewState {}

class ReviewsLoaded extends ReviewState {
  final List<ReviewModel> reviews;

  ReviewsLoaded(this.reviews);
}

class ReviewStatsLoaded extends ReviewState {
  final ReviewStatsModel stats;

  ReviewStatsLoaded(this.stats);
}

class ReviewsWithStatsLoaded extends ReviewState {
  final List<ReviewModel> reviews;
  final ReviewStatsModel stats;

  ReviewsWithStatsLoaded(this.reviews, this.stats);
}

class ReviewSuccess extends ReviewState {
  final ReviewModel review;

  ReviewSuccess(this.review);
}

class ReviewDeleted extends ReviewState {}

class ReviewReported extends ReviewState {}

class ReviewLikeToggled extends ReviewState {}

class ReviewEligibilityChecked extends ReviewState {
  final bool canReview;

  ReviewEligibilityChecked(this.canReview);
}

class ReviewError extends ReviewState {
  final String message;

  ReviewError(this.message);
}
