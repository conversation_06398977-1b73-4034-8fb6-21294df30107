import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/feature/dashboard/presentation/property_creation_form.dart';
import 'package:gather_point/generated/l10n.dart';

class MyListingScreen extends StatefulWidget {
  const MyListingScreen({super.key});

  @override
  State<MyListingScreen> createState() => _MyListingScreenState();
}

class _MyListingScreenState extends State<MyListingScreen> {
  final List<Map<String, dynamic>> _mockListings = [
    {
      'id': '1',
      'title': 'فيلا عصرية في الرياض',
      'description': 'فيلا واسعة 5 غرف نوم مع حديقة ومسبح',
      'price': 12500,
      'location': 'الرياض، حي النخيل',
      'bedrooms': 5,
      'bathrooms': 4,
      'area': 450,
      'status': 'active',
      'views': 245,
      'bookings': 12,
      'rating': 4.8,
      'image': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
    },
    {
      'id': '2',
      'title': 'شاليه مطل على البحر',
      'description': 'شاليه غرفتين نوم مع إطلالة بحرية ووصول للشاطئ',
      'price': 7500,
      'location': 'جدة، الكورنيش',
      'bedrooms': 2,
      'bathrooms': 2,
      'area': 120,
      'status': 'pending',
      'views': 89,
      'bookings': 5,
      'rating': 4.5,
      'image': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
    },
    {
      'id': '3',
      'title': 'شقة فاخرة في القاهرة الجديدة',
      'description': 'شقة 3 غرف نوم مفروشة بالكامل في كمبوند مغلق',
      'price': 9800,
      'location': 'القاهرة الجديدة، التجمع الخامس',
      'bedrooms': 3,
      'bathrooms': 2,
      'area': 180,
      'status': 'inactive',
      'views': 156,
      'bookings': 8,
      'rating': 4.2,
      'image': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400',
    },
  ];

  int _selectedFilter = 0;
  late final List<String> _filters;

  List<Map<String, dynamic>> get _filteredListings {
    if (_selectedFilter == 0) return _mockListings;

    final statusMap = {
      1: 'active',
      2: 'pending',
      3: 'inactive',
    };

    return _mockListings.where((listing) =>
      listing['status'] == statusMap[_selectedFilter]
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    _filters = [s.all, s.active, s.underReview, s.inactive];

    return EnhancedPageLayout(
      title: s.myListings,
      showBackButton: false,
      hasBottomNavigation: true,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                context.accentColor,
                context.accentColor.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.add_rounded,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PropertyCreationForm(),
                ),
              );
            },
            tooltip: 'إضافة عقار جديد',
          ),
        ),
      ],
      body: _mockListings.isEmpty
          ? EnhancedEmptyState(
              icon: Icons.home_work_outlined,
              title: 'لا توجد عقارات',
              subtitle: 'ابدأ بإضافة عقارك الأول',
              actionText: 'إضافة عقار',
              onActionPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PropertyCreationForm(),
                  ),
                );
              },
            )
          : Column(
              children: [
                // Stats Overview
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: _StatCard(
                          title: s.totalProperties,
                          value: '${_mockListings.length}',
                          icon: Icons.home_rounded,
                          color: context.accentColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _StatCard(
                          title: s.totalViews,
                          value: '${_mockListings.fold(0, (sum, item) => sum + (item['views'] as int))}',
                          icon: Icons.visibility_rounded,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _StatCard(
                          title: s.totalReservations,
                          value: '${_mockListings.fold(0, (sum, item) => sum + (item['bookings'] as int))}',
                          icon: Icons.calendar_today_rounded,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),

                // Filter Tabs
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SizedBox(
                    height: 40,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: _filters.length,
                      separatorBuilder: (_, __) => const SizedBox(width: 8),
                      itemBuilder: (context, index) {
                        final isSelected = index == _selectedFilter;
                        return GestureDetector(
                          onTap: () => setState(() => _selectedFilter = index),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                            decoration: BoxDecoration(
                              color: isSelected ? context.accentColor : Colors.transparent,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: isSelected ? context.accentColor : context.secondaryTextColor.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Text(
                              _filters[index],
                              style: AppTextStyles.font14SemiBold.copyWith(
                                color: isSelected ? Colors.white : context.primaryTextColor,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

                // Listings List
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredListings.length,
                    itemBuilder: (context, index) {
                      return _ListingCard(listing: _filteredListings[index]);
                    },
                  ),
                ),
              ],
            ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: AppTextStyles.font10Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
        ],
      ),
    );
  }
}

class _ListingCard extends StatelessWidget {
  final Map<String, dynamic> listing;

  const _ListingCard({required this.listing});

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'inactive':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status, S s) {
    switch (status) {
      case 'active':
        return s.active;
      case 'pending':
        return s.underReview;
      case 'inactive':
        return s.inactive;
      default:
        return 'غير معروف';
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final statusColor = _getStatusColor(listing['status']);

    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Image and Status
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  listing['image'],
                  height: 180,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    height: 180,
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported_rounded,
                      color: context.secondaryTextColor,
                      size: 48,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getStatusText(listing['status'], s),
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 12,
                left: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star_rounded,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${listing['rating']}',
                        style: AppTextStyles.font12SemiBold.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Property Details
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      listing['title'],
                      style: AppTextStyles.font18Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_rounded,
                          size: 16,
                          color: context.secondaryTextColor,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            listing['location'],
                            style: AppTextStyles.font14Regular.copyWith(
                              color: context.secondaryTextColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${listing['price']} ر.س',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Property Features
          Row(
            children: [
              _FeatureItem(
                icon: Icons.bed_rounded,
                value: '${listing['bedrooms']}',
                label: s.bedrooms,
              ),
              const SizedBox(width: 16),
              _FeatureItem(
                icon: Icons.bathtub_rounded,
                value: '${listing['bathrooms']}',
                label: s.bathrooms,
              ),
              const SizedBox(width: 16),
              _FeatureItem(
                icon: Icons.square_foot_rounded,
                value: '${listing['area']}',
                label: 'م²',
              ),
              const Spacer(),
              _StatItem(
                icon: Icons.visibility_rounded,
                value: '${listing['views']}',
                color: Colors.blue,
              ),
              const SizedBox(width: 12),
              _StatItem(
                icon: Icons.calendar_today_rounded,
                value: '${listing['bookings']}',
                color: Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: EnhancedButton(
                  text: s.viewDetails,
                  onPressed: () {},
                  isOutlined: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: EnhancedButton(
                  text: s.editProperty,
                  onPressed: () {},
                  icon: Icons.edit_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _FeatureItem extends StatelessWidget {
  final IconData icon;
  final String value;
  final String label;

  const _FeatureItem({
    required this.icon,
    required this.value,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: context.secondaryTextColor,
        ),
        const SizedBox(width: 4),
        Text(
          '$value $label',
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
      ],
    );
  }
}

class _StatItem extends StatelessWidget {
  final IconData icon;
  final String value;
  final Color color;

  const _StatItem({
    required this.icon,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: AppTextStyles.font12SemiBold.copyWith(
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
