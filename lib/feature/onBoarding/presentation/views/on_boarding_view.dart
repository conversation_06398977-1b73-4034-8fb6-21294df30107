import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/components/secondary_button.dart';
import 'package:gather_point/core/managers/app_initialization_cubit/app_initialization_cubit.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_cubit.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/feature/onBoarding/data/model/onboarding.dart';
import 'package:gather_point/feature/onBoarding/presentation/manager/Onboarding_cubit/onboarding_cubit.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

class OnBoardingView extends StatefulWidget {
  const OnBoardingView({super.key});

  @override
  State<OnBoardingView> createState() => _OnBoardingViewState();
}

class _OnBoardingViewState extends State<OnBoardingView> {
  final PageController pageController = PageController(initialPage: 0);

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  void skipOnBoarding() {
    SoundManager.playClickSound();
    context.read<AppInitializationCubit>().completeOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    OnboardingCubit cubit = BlocProvider.of<OnboardingCubit>(context);

    return Scaffold(
      body: SafeArea(
        child: BlocListener<AppInitializationCubit, AppInitializationState>(
          listener: (context, state) {
            if (state is AppInitializationNavigateToHome) {
              if (mounted) {
                GoRouter.of(context).go(RoutesKeys.kHomeViewTab);
              }
            }
          },
          child: BlocBuilder<OnboardingCubit, OnboardingState>(
            builder: (context, state) {
            return Column(
              children: [
                Expanded(
                  child: PageView.builder(
                    controller: pageController,
                    itemCount: OnboardingModel.onboardingItem.length,
                    onPageChanged: (int page) => cubit.changePage(page),
                    itemBuilder: (context, index) {
                      OnboardingModel model =
                      OnboardingModel.onboardingItem[index];
                      return Column(
                        children: [
                          SizedBox(
                            height: 324,
                            child: Image.asset(
                              model.image,
                              fit: BoxFit.fitWidth,
                              width: double.infinity,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              children: [
                                const SizedBox(height: 40),
                                RichText(
                                  text: TextSpan(
                                    text: '${model.title} ',
                                    style: AppTextStyles.font28Bold.copyWith(
                                      color: AppColors.yellow,
                                    ),
                                    children: <TextSpan>[
                                      TextSpan(
                                        text: model.subTitle,
                                        style:
                                        AppTextStyles.font28Bold.copyWith(
                                          color: context.watch<ThemeCubit>().state ==
                                              AppThemeMode.light
                                              ? AppColors.black
                                              : AppColors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 24),
                                Text(
                                  model.description,
                                  style: AppTextStyles.font16Regular.copyWith(
                                    color: context.watch<ThemeCubit>().state ==
                                        AppThemeMode.light
                                        ? AppColors.black
                                        : AppColors.white,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      SecondaryButton(
                        onPressed: skipOnBoarding,
                        label: s.skip,
                        fullWidth: false,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      const Spacer(),
                      DotsIndicator(
                        position: cubit.currentPage,
                        dotsCount: OnboardingModel.onboardingItem.length,
                        decorator: DotsDecorator(
                          size: const Size(12, 12),
                          activeSize: const Size(18, 7),
                          activeShape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          spacing: const EdgeInsets.symmetric(horizontal: 4),
                          activeColor: AppColors.yellow,
                          color: context.watch<ThemeCubit>().state ==
                              AppThemeMode.light
                              ? AppColors.black
                              : AppColors.white,
                        ),
                      ),
                      const Spacer(),
                      PrimaryButton(
                        onPressed: () {
                          SoundManager.playClickSound();
                          if (cubit.currentPage == 2) {
                            skipOnBoarding();
                          } else {
                            pageController.nextPage(
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeIn,
                            );
                          }
                        },
                        label: cubit.currentPage != 2 ? s.next : s.start,
                        fullWidth: false,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
        ),
      ),
    );
  }
}
