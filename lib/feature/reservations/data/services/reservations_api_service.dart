import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';

class ReservationsApiService {
  final DioConsumer _dioConsumer;

  ReservationsApiService(this._dioConsumer);

  /// Get user's reservations (guest view)
  Future<List<ReservationModel>> getUserReservations({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (status != null) 'status': status,
      };

      final response = await _dioConsumer.get(
        EndPoints.reservationsList,
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ReservationModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch reservations');
      }
    } catch (e) {
      throw Exception('Failed to fetch reservations: ${e.toString()}');
    }
  }

  /// Check reservation availability and pricing
  Future<ReservationCheckResult> checkReservation({
    required int serviceCategoryItemId,
    required String reservationFrom,
    required String reservationTo,
  }) async {
    try {
      final data = {
        'service_category_item_id': serviceCategoryItemId,
        'reservation_from': reservationFrom,
        'reservation_to': reservationTo,
      };

      final response = await _dioConsumer.post(
        EndPoints.reservationsCheck,
        data: data,
      );

      if (response['success'] == true) {
        return ReservationCheckResult.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Reservation check failed');
      }
    } catch (e) {
      throw Exception('Failed to check reservation: ${e.toString()}');
    }
  }

  /// Create a new reservation
  Future<ReservationModel> createReservation({
    required int serviceCategoryItemId,
    required String reservationFrom,
    required String reservationTo,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final data = {
        'service_category_item_id': serviceCategoryItemId,
        'reservation_from': reservationFrom,
        'reservation_to': reservationTo,
        ...?additionalData,
      };

      final response = await _dioConsumer.post(
        EndPoints.reservationsCreate,
        data: data,
      );

      if (response['success'] == true) {
        return ReservationModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create reservation');
      }
    } catch (e) {
      throw Exception('Failed to create reservation: ${e.toString()}');
    }
  }

  /// Cancel a reservation
  Future<bool> cancelReservation(int reservationId) async {
    try {
      final response = await _dioConsumer.delete(
        '/api/reservations/cancel/$reservationId',
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to cancel reservation: ${e.toString()}');
    }
  }

  /// Confirm a reservation (host action)
  Future<bool> confirmReservation(int reservationId) async {
    try {
      final response = await _dioConsumer.post(
        '/api/reservations/confirm/$reservationId',
        data: {'confirmed': true},
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to confirm reservation: ${e.toString()}');
    }
  }

  /// Get reservation details
  Future<ReservationModel> getReservationDetails(int reservationId) async {
    try {
      final response = await _dioConsumer.get(
        '/api/reservations/$reservationId',
      );

      if (response['success'] == true) {
        return ReservationModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch reservation details');
      }
    } catch (e) {
      throw Exception('Failed to fetch reservation details: ${e.toString()}');
    }
  }

  /// Get host's property reservations
  Future<List<ReservationModel>> getHostReservations({
    int page = 1,
    int limit = 10,
    String? status,
    int? propertyId,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        'host_view': true,
        if (status != null) 'status': status,
        if (propertyId != null) 'property_id': propertyId,
      };

      final response = await _dioConsumer.get(
        EndPoints.reservationsList,
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ReservationModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch host reservations');
      }
    } catch (e) {
      throw Exception('Failed to fetch host reservations: ${e.toString()}');
    }
  }

  /// Update reservation status
  Future<bool> updateReservationStatus(int reservationId, String status) async {
    try {
      final response = await _dioConsumer.patch(
        '/api/reservations/$reservationId/status',
        data: {'status': status},
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update reservation status: ${e.toString()}');
    }
  }
}

class ReservationCheckResult {
  final String unitTitle;
  final int totalDays;
  final double price;
  final double commission;
  final double finalPrice;
  final Map<String, dynamic> priceDetails;

  const ReservationCheckResult({
    required this.unitTitle,
    required this.totalDays,
    required this.price,
    required this.commission,
    required this.finalPrice,
    required this.priceDetails,
  });

  factory ReservationCheckResult.fromJson(Map<String, dynamic> json) {
    return ReservationCheckResult(
      unitTitle: json['unit_title'] ?? '',
      totalDays: json['total_days'] ?? 1,
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      commission: double.tryParse(json['commission']?.toString() ?? '0') ?? 0.0,
      finalPrice: double.tryParse(json['final_price']?.toString() ?? '0') ?? 0.0,
      priceDetails: json['price_details'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'unit_title': unitTitle,
      'total_days': totalDays,
      'price': price,
      'commission': commission,
      'final_price': finalPrice,
      'price_details': priceDetails,
    };
  }
}
