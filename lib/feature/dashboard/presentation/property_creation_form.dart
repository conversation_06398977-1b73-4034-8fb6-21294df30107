import 'dart:io';
import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/dashboard/presentation/open_street_map_picker_screen.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:hive/hive.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:reorderables/reorderables.dart';

class Facility {
  final int id;
  final String title;
  final String icon;
  final int? count;

  Facility({
    required this.id,
    required this.title,
    required this.icon,
    this.count,
  });

  factory Facility.fromJson(Map<String, dynamic> json) {
    return Facility(
      id: json['id'],
      title: json['title'],
      icon: json['icon'],
      count: json['count'], // ممكن تكون null
    );
  }
}

class PropertyCreationForm extends StatefulWidget {
  const PropertyCreationForm({super.key});

  @override
  _PropertyCreationFormState createState() => _PropertyCreationFormState();
}

class _PropertyCreationFormState extends State<PropertyCreationForm>
    with TickerProviderStateMixin {
  String? _itemId;
  late final DioConsumer _dioConsumer;
  bool _isLoading = false;
  int _currentStep = 0;

  // Advanced animation controllers
  late AnimationController _progressAnimationController;
  late AnimationController _stepAnimationController;
  late AnimationController _celebrationController;
  late AnimationController _shakeController;

  // Form validation and state
  final Map<int, bool> _stepCompletionStatus = {};
  final Map<String, String> _validationErrors = {};
  final bool _autoSaveEnabled = true;
  DateTime? _lastAutoSave;
  Timer? _autoSaveTimer;

  // Enhanced UI state
  final bool _showAdvancedOptions = false;
  final bool _isDragMode = false;
  int? _hoveredImageIndex;
  final bool _showValidationHints = true;
  final Set<int> _visitedSteps = {};

  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _dailyPriceController = TextEditingController();
  final _weeklyPriceController = TextEditingController();
  final _monthlyPriceController = TextEditingController();
  final _bathroomsController = TextEditingController();
  final _bedroomsController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bookingPolicyController = TextEditingController();
  final _cancellationPolicyController = TextEditingController();

  LatLng? _location;
  final List<File> _imageGallery = [];
  final List<int> _facilities = [];
  bool _includeCommissionDaily = false;
  bool _includeCommissionWeekly = false;
  bool _includeCommissionMonthly = false;

  final ImagePicker _picker = ImagePicker();
  List<ServiceCategory> _categories = [];
  int? _selectedCategoryId;
  bool _isLoadingCategories = false;

  List<Facility> _availableFacilities = [];
  bool _isLoadingFacilities = false;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );

    // Initialize animation controllers
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Initialize auto-save
    _setupAutoSave();

    _loadServiceCategories(); // Load categories
    _loadFacilities(); // Load facilities
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _stepAnimationController.dispose();
    _celebrationController.dispose();
    _shakeController.dispose();
    _autoSaveTimer?.cancel();
    super.dispose();
  }

  /// Setup auto-save functionality
  void _setupAutoSave() {
    if (_autoSaveEnabled) {
      _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        _performAutoSave();
      });
    }
  }

  /// Perform auto-save
  void _performAutoSave() {
    if (_hasUnsavedChanges()) {
      _lastAutoSave = DateTime.now();
      // Save draft to local storage or server
      _saveDraft();
    }
  }

  /// Check if there are unsaved changes
  bool _hasUnsavedChanges() {
    return _titleController.text.isNotEmpty ||
        _descriptionController.text.isNotEmpty ||
        _location != null ||
        _imageGallery.isNotEmpty ||
        _facilities.isNotEmpty;
  }

  /// Save draft functionality
  void _saveDraft() {
    // Implementation for saving draft
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.save_outlined, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Text(
                'Draft auto-saved',
                style: AppTextStyles.font12Medium.copyWith(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: Colors.green.withValues(alpha: 0.8),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
          margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
        ),
      );
    }
  }

  /// Advanced form validation
  bool _validateCurrentStep() {
    _validationErrors.clear();
    bool isValid = true;

    switch (_currentStep) {
      case 0: // Title & Description
        if (_titleController.text.trim().isEmpty) {
          _validationErrors['title'] =
              '${S.of(context).propertyTitle} is required';
          isValid = false;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _validationErrors['description'] =
              '${S.of(context).propertyDescription} is required';
          isValid = false;
        }
        if (_selectedCategoryId == null) {
          _validationErrors['category'] =
              '${S.of(context).selectCategory} is required';
          isValid = false;
        }
        break;
      case 1: // Location
        if (_location == null) {
          _validationErrors['location'] =
              '${S.of(context).location} is required';
          isValid = false;
        }
        break;
      case 2: // Images
        if (_imageGallery.isEmpty) {
          _validationErrors['images'] = 'At least one image is required';
          isValid = false;
        }
        break;
      case 3: // Facilities
        if (_facilities.isEmpty) {
          _validationErrors['facilities'] =
              'At least one facility must be selected';
          isValid = false;
        }
        break;
      case 4: // Pricing
        if (_dailyPriceController.text.trim().isEmpty) {
          _validationErrors['dailyPrice'] =
              '${S.of(context).dailyPrice} is required';
          isValid = false;
        }
        break;
      case 5: // Details
        if (_bathroomsController.text.trim().isEmpty) {
          _validationErrors['bathrooms'] =
              '${S.of(context).numberOfBathrooms} is required';
          isValid = false;
        }
        if (_bedroomsController.text.trim().isEmpty) {
          _validationErrors['bedrooms'] =
              '${S.of(context).numberOfBedrooms} is required';
          isValid = false;
        }
        if (_guestsController.text.trim().isEmpty) {
          _validationErrors['guests'] =
              '${S.of(context).numberOfGuests} is required';
          isValid = false;
        }
        break;
    }

    // Update step completion status
    _stepCompletionStatus[_currentStep] = isValid;
    _visitedSteps.add(_currentStep);

    if (!isValid) {
      _shakeController.forward().then((_) => _shakeController.reset());
      HapticFeedback.lightImpact();
    }

    return isValid;
  }

  /// Get completion percentage
  double get _completionPercentage {
    int completedSteps =
        _stepCompletionStatus.values.where((completed) => completed).length;
    return completedSteps / 6.0;
  }

  /// Check if step is completed
  bool _isStepCompleted(int step) {
    return _stepCompletionStatus[step] ?? false;
  }

  /// Get step icon based on completion status
  IconData _getStepIcon(int step) {
    if (_isStepCompleted(step)) {
      return Icons.check_circle;
    } else if (_visitedSteps.contains(step)) {
      return Icons.error_outline;
    } else {
      return Icons.radio_button_unchecked;
    }
  }

  /// Get step icon color
  Color _getStepIconColor(BuildContext context, int step) {
    if (_isStepCompleted(step)) {
      return Colors.green;
    } else if (_visitedSteps.contains(step) && !_isStepCompleted(step)) {
      return Colors.orange;
    } else {
      return context.secondaryTextColor;
    }
  }

  /// Show validation errors in a beautiful dialog
  void _showValidationErrors() {
    if (_validationErrors.isEmpty) return;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: dialogContext.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.warning_outlined,
                  color: Colors.orange, size: 24),
            ),
            const SizedBox(width: 12),
            Text(
              'Please Complete Required Fields',
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'The following fields need your attention:',
              style: AppTextStyles.font14Regular
                  .copyWith(color: dialogContext.secondaryTextColor),
            ),
            const SizedBox(height: 16),
            ..._validationErrors.entries
                .map((entry) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 6),
                            width: 4,
                            height: 4,
                            decoration: const BoxDecoration(
                              color: Colors.orange,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              entry.value,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: dialogContext.primaryTextColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ))
                ,
          ],
        ),
        actions: [
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(dialogContext),
            icon:
                const Icon(Icons.edit_outlined, color: Colors.white, size: 18),
            label: Text(
              'Fix Issues',
              style: AppTextStyles.font14Bold.copyWith(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadServiceCategories() async {
    setState(() => _isLoadingCategories = true);
    try {
      final response = await _dioConsumer.get(
        'https://backend.gatherpoint.sa/api/service_categories/list',
        queryParameters: {'service_category_id': 1},
      );
      if (response['data'] != null) {
        final List data = response['data'];
        final categories =
            data.map((json) => ServiceCategory.fromJson(json)).toList();
        setState(() {
          _categories = categories;
          _selectedCategoryId =
              categories.isNotEmpty ? categories.first.id : null;
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('${S.of(context).failedToLoadCategories}: $e');
      }
    } finally {
      setState(() => _isLoadingCategories = false);
    }
  }

  Future<void> _loadFacilities() async {
    setState(() => _isLoadingFacilities = true);
    try {
      final response = await _dioConsumer.get(
        'https://backend.gatherpoint.sa/api/facilities/list',
      );
      if (response['data'] != null) {
        final List data = response['data'];
        setState(() {
          _availableFacilities = data
              .map<Facility>((facility) => Facility.fromJson(facility))
              .toList();
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('${S.of(context).failedToLoadFacilities}: $e');
      }
    } finally {
      setState(() => _isLoadingFacilities = false);
    }
  }

  /// Pick image from camera
  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? pickedFile =
          await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        setState(() => _imageGallery.add(File(pickedFile.path)));
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      _showErrorDialog('Failed to take photo: $e');
    }
  }

  void _showErrorDialog(String message) {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: dialogContext.cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              s.error,
              style: AppTextStyles.font18Bold.copyWith(
                color: dialogContext.primaryTextColor,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: AppTextStyles.font16Regular.copyWith(
            color: dialogContext.secondaryTextColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            style: TextButton.styleFrom(
              backgroundColor: dialogContext.accentColor.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              s.ok,
              style: AppTextStyles.font16Medium.copyWith(
                color: dialogContext.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage() {
    final s = S.of(context);

    // Show celebration dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: dialogContext.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated success icon
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 600),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 60 * value,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),

            // Success title
            Text(
              '🎉 Congratulations!',
              style: AppTextStyles.font20Bold.copyWith(
                color: dialogContext.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Success message
            Text(
              s.propertyCreatedSuccessfully,
              style: AppTextStyles.font16Regular.copyWith(
                color: dialogContext.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: () {
                      Navigator.pop(dialogContext); // Go back to previous screen
                    },
                    icon: Icon(Icons.home_outlined,
                        color: dialogContext.secondaryTextColor),
                    label: Text(
                      'Go Home',
                      style: AppTextStyles.font14Medium.copyWith(
                        color: dialogContext.secondaryTextColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      // Reset form for new property
                      setState(() {
                        _currentStep = 0;
                        _itemId = null;
                        _titleController.clear();
                        _descriptionController.clear();
                        _selectedCategoryId = null;
                        _location = null;
                        _imageGallery.clear();
                        _facilities.clear();
                        _dailyPriceController.clear();
                        _weeklyPriceController.clear();
                        _monthlyPriceController.clear();
                        _bathroomsController.clear();
                        _bedroomsController.clear();
                        _guestsController.clear();
                        _bookingPolicyController.clear();
                        _cancellationPolicyController.clear();
                      });
                    },
                    icon: const Icon(Icons.add_circle_outline,
                        color: Colors.white),
                    label: Text(
                      'Create Another',
                      style: AppTextStyles.font14Bold
                          .copyWith(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    if (_isLoadingCategories) return const CircularProgressIndicator();
    return DropdownButtonFormField<int>(
      value: _selectedCategoryId,
      onChanged: (val) => setState(() => _selectedCategoryId = val),
      items: _categories
          .map((cat) => DropdownMenuItem(value: cat.id, child: Text(cat.title)))
          .toList(),
      decoration: const InputDecoration(labelText: 'اختر القسم'),
    );
  }

  Widget _buildFacilityIcons() {
    if (_isLoadingFacilities) {
      return const Center(child: CircularProgressIndicator());
    }

    return Wrap(
      spacing: 12,
      children: List.generate(_availableFacilities.length, (index) {
        final facility = _availableFacilities[index];
        final isSelected = _facilities.contains(facility.id);

        return ChoiceChip(
          avatar: facility.icon.isNotEmpty
              ? Image.network(
                  facility.icon,
                  width: 24,
                  height: 24,
                )
              : null,
          label: Text(facility.title),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _facilities.add(facility.id);
              } else {
                _facilities.remove(facility.id);
              }
            });
          },
          selectedColor: Colors.amber,
          backgroundColor: Colors.grey.shade300,
        );
      }),
    );
  }

  Widget _buildLocationPreview() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => OSMMapPicker(initialPosition: _location),
              ),
            );
            if (result != null && result is LatLng) {
              setState(() => _location = result);
            }
          },
          child: const Text('Pick Location'),
        ),
        if (_location != null)
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: FlutterMap(
              options: MapOptions(
                initialCenter: _location!,
                initialZoom: 13.0,
              ),
              children: [
                TileLayer(
                  urlTemplate:
                      'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                  subdomains: const ['a', 'b', 'c'],
                ),
                MarkerLayer(
                  markers: [
                    Marker(
                      point: _location!,
                      child: const Icon(Icons.location_pin,
                          color: Colors.red, size: 40),
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildImageGrid() {
    if (_imageGallery.isEmpty) {
      return GestureDetector(
        onTap: _pickImage,
        child: Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text('Tap to upload images'),
          ),
        ),
      );
    }

    return ReorderableWrap(
      spacing: 8,
      runSpacing: 8,
      children: _imageGallery
          .map((image) => Stack(
                key: ValueKey(image),
                children: [
                  Image.file(image, width: 100, height: 100, fit: BoxFit.cover),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colors.red),
                      onPressed: () =>
                          setState(() => _imageGallery.remove(image)),
                    ),
                  )
                ],
              ))
          .toList(),
      onReorder: (oldIndex, newIndex) {
        setState(() {
          final image = _imageGallery.removeAt(oldIndex);
          _imageGallery.insert(newIndex, image);
        });
      },
    );
  }

  Widget _buildTextInput(String label, TextEditingController controller,
      {int maxLines = 1, String? errorText}) {
    return TextField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        errorText: errorText, // Show error message
      ),
    );
  }

  Future<void> _createItem() async {
    setState(() => _isLoading = true);
    try {
      final response = await _dioConsumer.post(
        'https://backend.gatherpoint.sa/api/items/create',
        data: {
          'title': _titleController.text,
          'content': _descriptionController.text,
          'service_category_id': _selectedCategoryId,
        },
      );
      _itemId = response['data']['id'].toString();
    } catch (e) {
      if (mounted) {
        _showErrorDialog('${S.of(context).failedToCreateItem}: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateLocation() async {
    if (_location == null || _itemId == null) return;
    await _dioConsumer.put(
        'https://backend.gatherpoint.sa/api/items/update/$_itemId',
        data: {
          'lat': _location!.latitude,
          'lon': _location!.longitude,
        },
        isFormData: false);
  }

  /// Enhanced image picker with multiple selection
  Future<void> _pickImage() async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage();
      if (pickedFiles.isNotEmpty) {
        setState(() {
          for (final file in pickedFiles) {
            _imageGallery.add(File(file.path));
          }
        });

        // Show success feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.photo_library_outlined,
                      color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '${pickedFiles.length} image${pickedFiles.length > 1 ? 's' : ''} added!',
                    style: AppTextStyles.font14Medium
                        .copyWith(color: Colors.white),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
              duration: const Duration(seconds: 2),
            ),
          );

          HapticFeedback.selectionClick();
        }
      }
    } catch (e) {
      _showErrorDialog('Failed to pick images: $e');
    }
  }

  Future<void> _uploadImages() async {
    if (_imageGallery.isEmpty || _itemId == null) return;

    final formData = FormData.fromMap({
      'gallery[]': _imageGallery
          .map(
            (file) => MultipartFile.fromFileSync(file.path,
                filename: file.path.split('/').last),
          )
          .toList(),
    });

    await _dioConsumer.post(
      'https://backend.gatherpoint.sa/api/items/upload-gallery/$_itemId',
      data: formData,
      isFormData: true,
    );
  }

  Future<void> _updateFacilities() async {
    if (_itemId == null) return;
    await _dioConsumer.put(
      'https://backend.gatherpoint.sa/api/items/update/$_itemId',
      data: {
        'facilities': _facilities, // Changed from 'facilities[]' to 'facilities'
      },
      isFormData: false,
    );
  }

  Future<void> _updatePrices() async {
    if (_itemId == null) return;
    await _dioConsumer.put(
        'https://backend.gatherpoint.sa/api/items/update/$_itemId',
        data: {
          'price': _dailyPriceController.text,
          'week_price': _weeklyPriceController.text,
          'month_price': _monthlyPriceController.text,
          'include_commission_daily': _includeCommissionDaily,
          'include_commission_weekly': _includeCommissionWeekly,
          'include_commission_monthly': _includeCommissionMonthly,
        },
        isFormData: false);
  }

  Future<void> _updateDetails() async {
    if (_itemId == null) return;
    await _dioConsumer.put(
        'https://backend.gatherpoint.sa/api/items/update/$_itemId',
        data: {
          'no_guests': _guestsController.text,
          'beds': _bedroomsController.text,
          'baths': _bathroomsController.text,
          'booking_rules': _bookingPolicyController.text,
          'cancelation_rules': _cancellationPolicyController.text,
        },
        isFormData: false);
  }

  void _submitForm() {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: dialogContext.cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: dialogContext.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.check_circle_outline,
                color: dialogContext.accentColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              s.confirmSubmission,
              style: AppTextStyles.font18Bold.copyWith(
                color: dialogContext.primaryTextColor,
              ),
            ),
          ],
        ),
        content: Text(
          s.confirmSubmissionMessage,
          style: AppTextStyles.font16Regular.copyWith(
            color: dialogContext.secondaryTextColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              s.cancel,
              style: AppTextStyles.font16Medium.copyWith(
                color: dialogContext.secondaryTextColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // Proceed with final submission
              await _finalizePropertySubmission();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: dialogContext.accentColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              s.submit,
              style: AppTextStyles.font16Bold.copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceField(TextEditingController controller, String label,
      bool includeCommission, ValueChanged<bool> onChanged) {
    final s = S.of(context);
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label ${s.enterPrice}',
            style: AppTextStyles.font14Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  style: TextStyle(color: context.primaryTextColor),
                  decoration: InputDecoration(
                    hintText: '${s.enterPrice} $label',
                    hintStyle: TextStyle(color: context.secondaryTextColor),
                    prefixIcon: Icon(
                      Icons.attach_money,
                      color: context.accentColor,
                    ),
                    filled: true,
                    fillColor: context.isDarkMode
                        ? Colors.grey[800]
                        : Colors.grey[100],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: context.accentColor, width: 2),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: includeCommission
                      ? context.accentColor.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: context.accentColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      value: includeCommission,
                      onChanged: (val) => onChanged(val ?? false),
                      activeColor: context.accentColor,
                    ),
                    Text(
                      s.commission,
                      style: AppTextStyles.font12Medium.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Enhanced text field with theme awareness, icons, and validation
  Widget _buildEnhancedTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required BuildContext context,
    int maxLines = 1,
    TextInputType? keyboardType,
    bool required = false,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: context.accentColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Focus(
        child: Builder(
          builder: (context) {
            final hasFocus = Focus.of(context).hasFocus;
            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: hasFocus
                      ? context.accentColor
                      : context.isDarkMode
                          ? Colors.grey[700]!
                          : Colors.grey[300]!,
                  width: hasFocus ? 2 : 1,
                ),
              ),
              child: TextField(
                controller: controller,
                maxLines: maxLines,
                keyboardType: keyboardType,
                style: TextStyle(color: context.primaryTextColor),
                decoration: InputDecoration(
                  labelText: required ? '$label *' : label,
                  labelStyle: TextStyle(
                    color: hasFocus
                        ? context.accentColor
                        : context.secondaryTextColor,
                    fontWeight: hasFocus ? FontWeight.w600 : FontWeight.normal,
                  ),
                  prefixIcon: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.all(12),
                    child: Icon(
                      icon,
                      color: hasFocus
                          ? context.accentColor
                          : context.secondaryTextColor,
                      size: 20,
                    ),
                  ),
                  suffixIcon: controller.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: context.secondaryTextColor,
                            size: 18,
                          ),
                          onPressed: () {
                            controller.clear();
                            // Trigger rebuild to hide clear button
                            (context as Element).markNeedsBuild();
                          },
                        )
                      : null,
                  filled: true,
                  fillColor: context.cardColor,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 16,
                  ),
                ),
                onChanged: (value) {
                  // Trigger rebuild to show/hide clear button
                  (context as Element).markNeedsBuild();
                },
              ),
            );
          },
        ),
      ),
    );
  }

  /// Enhanced progress indicator with validation and completion tracking
  Widget _buildProgressIndicator(BuildContext context, S s) {
    final steps = [
      s.titleAndDescription,
      s.location,
      s.imageGallery,
      s.availableServices,
      s.pricing,
      s.bookingDetails,
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Enhanced Progress Bar with Completion Tracking
          Row(
            children: [
              // Step indicators
              Expanded(
                child: Row(
                  children: List.generate(steps.length, (index) {
                    final isActive = index == _currentStep;
                    final isCompleted = _isStepCompleted(index);
                    final isVisited = _visitedSteps.contains(index);

                    return Expanded(
                      child: Container(
                        margin: EdgeInsets.only(
                            right: index < steps.length - 1 ? 4 : 0),
                        child: Column(
                          children: [
                            // Step circle
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color: isCompleted
                                    ? Colors.green
                                    : isActive
                                        ? context.accentColor
                                        : isVisited
                                            ? Colors.orange
                                                .withValues(alpha: 0.3)
                                            : context.isDarkMode
                                                ? Colors.grey[700]
                                                : Colors.grey[300],
                                shape: BoxShape.circle,
                                boxShadow: isActive
                                    ? [
                                        BoxShadow(
                                          color: context.accentColor
                                              .withValues(alpha: 0.3),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]
                                    : null,
                              ),
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: Icon(
                                  _getStepIcon(index),
                                  key: ValueKey(
                                      '${index}_${isCompleted}_$isVisited'),
                                  size: 14,
                                  color: isCompleted || isActive
                                      ? Colors.white
                                      : context.secondaryTextColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                            // Progress line
                            if (index < steps.length - 1)
                              Container(
                                height: 2,
                                decoration: BoxDecoration(
                                  color: isCompleted
                                      ? Colors.green
                                      : context.isDarkMode
                                          ? Colors.grey[700]
                                          : Colors.grey[300],
                                  borderRadius: BorderRadius.circular(1),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
              const SizedBox(width: 12),
              // Completion percentage
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      context.accentColor.withValues(alpha: 0.1),
                      context.accentColor.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: context.accentColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.analytics_outlined,
                      size: 14,
                      color: context.accentColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${(_completionPercentage * 100).round()}%',
                      style: AppTextStyles.font12Bold.copyWith(
                        color: context.accentColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Current Step Title with Status
          Row(
            children: [
              Expanded(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    steps[_currentStep],
                    key: ValueKey(_currentStep),
                    style: AppTextStyles.font16Bold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                ),
              ),
              if (_validationErrors.isNotEmpty &&
                  _visitedSteps.contains(_currentStep))
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.orange.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.warning_outlined,
                        size: 12,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Incomplete',
                        style: AppTextStyles.font10Bold.copyWith(
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),

          // Auto-save indicator
          if (_lastAutoSave != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  const Icon(
                    Icons.cloud_done_outlined,
                    size: 12,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Auto-saved ${_getTimeAgo(_lastAutoSave!)}',
                    style: AppTextStyles.font10Regular.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Get time ago string
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${difference.inHours}h ago';
    }
  }

  /// Enhanced facility icons with better UI
  Widget _buildEnhancedFacilityIcons() {
    if (_isLoadingFacilities) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
        ),
      );
    }

    if (_availableFacilities.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: context.accentColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: context.accentColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              color: context.accentColor,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              S.of(context).noResults,
              style: AppTextStyles.font14Medium.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: _availableFacilities.length,
      itemBuilder: (context, index) {
        final facility = _availableFacilities[index];
        final isSelected = _facilities.contains(facility.id);

        return GestureDetector(
          onTap: () {
            setState(() {
              if (isSelected) {
                _facilities.remove(facility.id);
              } else {
                _facilities.add(facility.id);
              }
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? context.accentColor : context.cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected
                    ? context.accentColor
                    : context.isDarkMode
                        ? Colors.grey[700]!
                        : Colors.grey[300]!,
                width: 2,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: context.accentColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Facility Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.white.withValues(alpha: 0.2)
                        : context.accentColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: facility.icon.isNotEmpty
                      ? Image.network(
                          facility.icon,
                          width: 24,
                          height: 24,
                          color:
                              isSelected ? Colors.white : context.accentColor,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.star_outline,
                            color:
                                isSelected ? Colors.white : context.accentColor,
                            size: 24,
                          ),
                        )
                      : Icon(
                          Icons.star_outline,
                          color:
                              isSelected ? Colors.white : context.accentColor,
                          size: 24,
                        ),
                ),
                const SizedBox(height: 8),

                // Facility Title
                Text(
                  facility.title,
                  style: AppTextStyles.font12Bold.copyWith(
                    color: isSelected ? Colors.white : context.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                // Selection Indicator
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Helper method for preview items
  Widget _buildPreviewItem(
      String title, String value, IconData icon, BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.accentColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: context.accentColor, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font12Bold
                      .copyWith(color: context.accentColor),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppTextStyles.font14Regular
                      .copyWith(color: context.primaryTextColor),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced bottom navigation bar
  Widget _buildBottomNavigationBar(BuildContext context, S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Back Button
            if (_currentStep > 0)
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => setState(() => _currentStep--),
                  icon: const Icon(Icons.arrow_back, size: 18),
                  label: Text(
                    s.back,
                    style:
                        AppTextStyles.font14Bold.copyWith(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.isDarkMode
                        ? Colors.grey[700]
                        : Colors.grey[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

            if (_currentStep > 0) const SizedBox(width: 12),

            // Next/Submit Button
            Expanded(
              flex: _currentStep > 0 ? 2 : 1,
              child: ElevatedButton.icon(
                onPressed: _isLoading
                    ? null
                    : () async {
                        // Validate current step before proceeding
                        if (!_validateCurrentStep()) {
                          _showValidationErrors();
                          return;
                        }

                        setState(() => _isLoading = true);

                        try {
                          switch (_currentStep) {
                            case 0:
                              if (_itemId == null) await _createItem();
                              break;
                            case 1:
                              await _updateLocation();
                              break;
                            case 2:
                              await _uploadImages();
                              break;
                            case 3:
                              await _updateFacilities();
                              break;
                            case 4:
                              await _updatePrices();
                              break;
                            case 5:
                              await _updateDetails();
                              _celebrationController.forward();
                              _submitForm();
                              return;
                          }

                          // Animate progress and move to next step
                          _progressAnimationController.forward();

                          setState(() {
                            _isLoading = false;
                            if (_itemId != null && _currentStep < 5) {
                              _currentStep++;
                              _stepAnimationController.forward().then((_) {
                                _stepAnimationController.reset();
                              });
                            }
                          });

                          // Haptic feedback for successful step completion
                          HapticFeedback.selectionClick();
                        } catch (e) {
                          setState(() => _isLoading = false);
                          _showErrorDialog('Error: $e');
                        }
                      },
                icon: _isLoading
                    ? const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          _currentStep == 5
                              ? Icons.check_circle
                              : Icons.arrow_forward,
                          key: ValueKey(_currentStep),
                          size: 18,
                        ),
                      ),
                label: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: Text(
                    _isLoading
                        ? s.loading
                        : (_currentStep == 5 ? s.submit : s.next),
                    key: ValueKey('${_currentStep}_$_isLoading'),
                    style:
                        AppTextStyles.font14Bold.copyWith(color: Colors.white),
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      _currentStep == 5 ? Colors.green : context.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                  shadowColor:
                      (_currentStep == 5 ? Colors.green : context.accentColor)
                          .withValues(alpha: 0.3),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Floating action buttons for quick actions
  Widget _buildFloatingActionButtons(BuildContext context, S s) {
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Save Draft FAB
          AnimatedScale(
            scale: _currentStep > 0 ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: FloatingActionButton(
              heroTag: "save_draft",
              mini: true,
              backgroundColor: context.accentColor.withValues(alpha: 0.9),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.save_outlined,
                            color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text('Draft saved!',
                            style: AppTextStyles.font14Medium
                                .copyWith(color: Colors.white)),
                      ],
                    ),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    margin: const EdgeInsets.all(16),
                  ),
                );
              },
              child: const Icon(Icons.save_outlined,
                  color: Colors.white, size: 20),
            ),
          ),
          const SizedBox(height: 12),

          // Preview FAB
          AnimatedScale(
            scale: _currentStep > 2 ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: FloatingActionButton(
              heroTag: "preview",
              mini: true,
              backgroundColor:
                  context.isDarkMode ? Colors.grey[700] : Colors.grey[600],
              onPressed: () {
                _showPreviewDialog(context, s);
              },
              child: const Icon(Icons.preview_outlined,
                  color: Colors.white, size: 20),
            ),
          ),
        ],
      ),
    );
  }

  /// Preview dialog for property
  void _showPreviewDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: dialogContext.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: dialogContext.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.preview_outlined,
                  color: dialogContext.accentColor, size: 24),
            ),
            const SizedBox(width: 12),
            Text('Property Preview',
                style: AppTextStyles.font18Bold
                    .copyWith(color: dialogContext.primaryTextColor)),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_titleController.text.isNotEmpty) ...[
                  _buildPreviewItem('Title', _titleController.text, Icons.title,
                      dialogContext),
                  const SizedBox(height: 16),
                ],
                if (_descriptionController.text.isNotEmpty) ...[
                  _buildPreviewItem('Description', _descriptionController.text,
                      Icons.description_outlined, dialogContext),
                  const SizedBox(height: 16),
                ],
                if (_location != null) ...[
                  _buildPreviewItem(
                      'Location',
                      'Lat: ${_location!.latitude.toStringAsFixed(4)}, Lng: ${_location!.longitude.toStringAsFixed(4)}',
                      Icons.location_on_outlined,
                      dialogContext),
                  const SizedBox(height: 16),
                ],
                if (_imageGallery.isNotEmpty) ...[
                  _buildPreviewItem(
                      'Images',
                      '${_imageGallery.length} images uploaded',
                      Icons.photo_library_outlined,
                      dialogContext),
                  const SizedBox(height: 16),
                ],
                if (_facilities.isNotEmpty) ...[
                  _buildPreviewItem(
                      'Facilities',
                      '${_facilities.length} facilities selected',
                      Icons.room_service_outlined,
                      dialogContext),
                  const SizedBox(height: 16),
                ],
                if (_dailyPriceController.text.isNotEmpty) ...[
                  _buildPreviewItem(
                      'Daily Price',
                      '${_dailyPriceController.text} SAR',
                      Icons.attach_money,
                      dialogContext),
                  const SizedBox(height: 16),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.close, color: dialogContext.accentColor, size: 18),
            label: Text('Close',
                style: AppTextStyles.font14Medium
                    .copyWith(color: dialogContext.accentColor)),
          ),
        ],
      ),
    );
  }

  /// Finalize property submission by activating it
  Future<void> _finalizePropertySubmission() async {
    if (_itemId == null) return;

    setState(() => _isLoading = true);
    
    try {
      // Final update to activate the property
      await _dioConsumer.put(
        'https://backend.gatherpoint.sa/api/items/update/$_itemId',
        data: {
          'active': true, // Activate the property
        },
        isFormData: false,
      );

      // Show success message
      _showSuccessMessage();
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Failed to submit property: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    if (_isLoading) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
              ),
              const SizedBox(height: 16),
              Text(
                s.loading,
                style: AppTextStyles.font16Regular.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        title: Text(s.createProperty,
            style: AppTextStyles.font18Bold.copyWith(
                color: Theme.of(context).appBarTheme.titleTextStyle?.color ??
                    context.primaryTextColor)),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        elevation: 0.5,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).appBarTheme.iconTheme?.color ??
                context.primaryTextColor,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: _buildProgressIndicator(context, s),
        ),
      ),
      body: Stack(
        children: [
          Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                    primary: context.accentColor,
                  ),
            ),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 400),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(1.0, 0.0),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  )),
                  child: child,
                );
              },
              child: Stepper(
                key: ValueKey(_currentStep),
                type: StepperType.vertical,
                currentStep: _currentStep,
                onStepContinue: () async {
                  setState(() {
                    _isLoading = false;
                    if (_itemId != null && _currentStep < 5) _currentStep++;
                  });
                  switch (_currentStep) {
                    case 0:
                      if (_itemId == null) await _createItem();
                      break;
                    case 1:
                      await _updateLocation();
                      break;
                    case 2:
                      await _uploadImages();
                      break;
                    case 3:
                      await _updateFacilities();
                      break;
                    case 4:
                      await _updatePrices();
                      break;
                    case 5:
                      await _updateDetails();
                      _submitForm();
                      break;
                  }
                  setState(() {
                    _isLoading = false;
                    if (_itemId != null && _currentStep < 5) _currentStep++;
                  });
                },
                onStepCancel: () {
                  if (_currentStep > 0) setState(() => _currentStep--);
                },
                controlsBuilder: (context, details) {
                  return Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Next/Submit Button
                        ElevatedButton.icon(
                          onPressed: details.onStepContinue,
                          icon: Icon(
                            _currentStep == 5
                                ? Icons.check
                                : Icons.arrow_forward,
                            size: 18,
                          ),
                          label: Text(
                            _currentStep == 5 ? s.submit : s.next,
                            style: AppTextStyles.font14Bold
                                .copyWith(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.accentColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),

                        // Back and Skip buttons
                        Row(
                          children: [
                            if (_currentStep > 0)
                              TextButton.icon(
                                onPressed: details.onStepCancel,
                                icon: Icon(
                                  Icons.arrow_back,
                                  size: 16,
                                  color: context.secondaryTextColor,
                                ),
                                label: Text(
                                  s.back,
                                  style: AppTextStyles.font14Medium.copyWith(
                                    color: context.secondaryTextColor,
                                  ),
                                ),
                              ),
                            if (_currentStep < 5) ...[
                              const SizedBox(width: 8),
                              TextButton.icon(
                                onPressed: () =>
                                    setState(() => _currentStep = 5),
                                icon: Icon(
                                  Icons.skip_next,
                                  size: 16,
                                  color: context.secondaryTextColor,
                                ),
                                label: Text(
                                  s.skip,
                                  style: AppTextStyles.font14Medium.copyWith(
                                    color: context.secondaryTextColor,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  );
                },
                steps: [
                  Step(
                    title: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.edit_outlined,
                            color: context.accentColor,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          s.titleAndDescription,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    content: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: context.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: context.cardShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Property Title
                          _buildEnhancedTextField(
                            controller: _titleController,
                            label: s.propertyTitle,
                            icon: Icons.title,
                            context: context,
                          ),
                          const SizedBox(height: 16),

                          // Property Description
                          _buildEnhancedTextField(
                            controller: _descriptionController,
                            label: s.propertyDescription,
                            icon: Icons.description_outlined,
                            context: context,
                            maxLines: 3,
                          ),
                          const SizedBox(height: 20),

                          // Category Selection
                          Text(
                            s.selectCategory,
                            style: AppTextStyles.font16Bold.copyWith(
                              color: context.primaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _isLoadingCategories
                              ? Center(
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        context.accentColor),
                                  ),
                                )
                              : SizedBox(
                                  height: 70,
                                  child: ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: _categories.length,
                                    separatorBuilder: (_, __) =>
                                        const SizedBox(width: 12),
                                    itemBuilder: (context, index) {
                                      final category = _categories[index];
                                      final selected =
                                          category.id == _selectedCategoryId;

                                      return GestureDetector(
                                        onTap: () => setState(() =>
                                            _selectedCategoryId = category.id),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 12),
                                          decoration: BoxDecoration(
                                            color: selected
                                                ? context.accentColor
                                                : context.isDarkMode
                                                    ? Colors.grey[800]
                                                    : Colors.grey[200],
                                            borderRadius:
                                                BorderRadius.circular(16),
                                            border: Border.all(
                                              color: selected
                                                  ? context.accentColor
                                                  : Colors.transparent,
                                              width: 2,
                                            ),
                                            boxShadow: selected
                                                ? [
                                                    BoxShadow(
                                                      color: context.accentColor
                                                          .withValues(
                                                              alpha: 0.3),
                                                      blurRadius: 8,
                                                      offset:
                                                          const Offset(0, 2),
                                                    ),
                                                  ]
                                                : null,
                                          ),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              if (category.icon.isNotEmpty)
                                                Image.network(
                                                  category.icon,
                                                  width: 24,
                                                  height: 24,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      Icon(
                                                    Icons.category_outlined,
                                                    color: selected
                                                        ? Colors.white
                                                        : context.accentColor,
                                                    size: 24,
                                                  ),
                                                ),
                                              const SizedBox(height: 4),
                                              Text(
                                                category.title,
                                                style: AppTextStyles.font12Bold
                                                    .copyWith(
                                                  color: selected
                                                      ? Colors.white
                                                      : context
                                                          .primaryTextColor,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                        ],
                      ),
                    ),
                    isActive: _currentStep == 0,
                  ),
                  // Step 2: Location
                  Step(
                    title: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.location_on_outlined,
                            color: context.accentColor,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          s.location,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    content: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: context.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: context.cardShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Pick Location Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => OSMMapPicker(
                                        initialPosition: _location),
                                  ),
                                );
                                if (result != null && result is LatLng) {
                                  setState(() => _location = result);
                                }
                              },
                              icon: Icon(
                                _location != null
                                    ? Icons.edit_location
                                    : Icons.add_location,
                                size: 20,
                              ),
                              label: Text(
                                _location != null
                                    ? 'تحديث الموقع'
                                    : s.pickLocation,
                                style: AppTextStyles.font14Bold
                                    .copyWith(color: Colors.white),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: context.accentColor,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),

                          if (_location != null) ...[
                            const SizedBox(height: 20),
                            // Location Preview
                            Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: context.accentColor
                                      .withValues(alpha: 0.3),
                                  width: 2,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: FlutterMap(
                                  options: MapOptions(
                                    initialCenter: _location!,
                                    initialZoom: 13.0,
                                  ),
                                  children: [
                                    TileLayer(
                                      urlTemplate:
                                          'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                                      subdomains: const ['a', 'b', 'c'],
                                    ),
                                    MarkerLayer(
                                      markers: [
                                        Marker(
                                          point: _location!,
                                          child: Container(
                                            padding: const EdgeInsets.all(4),
                                            decoration: BoxDecoration(
                                              color: context.accentColor,
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: context.accentColor
                                                      .withValues(alpha: 0.3),
                                                  blurRadius: 8,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: const Icon(
                                              Icons.location_pin,
                                              color: Colors.white,
                                              size: 32,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),
                            // Location Coordinates
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color:
                                    context.accentColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: context.accentColor
                                      .withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.my_location,
                                    color: context.accentColor,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      '${s.latitude}: ${_location!.latitude.toStringAsFixed(6)}\n${s.longitude}: ${_location!.longitude.toStringAsFixed(6)}',
                                      style:
                                          AppTextStyles.font12Medium.copyWith(
                                        color: context.primaryTextColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    isActive: _currentStep == 1,
                  ),
                  // Step 3: Image Gallery
                  Step(
                    title: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.photo_library_outlined,
                            color: context.accentColor,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          s.imageGallery,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    content: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: context.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: context.cardShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Add Image Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _pickImage,
                              icon: const Icon(Icons.add_photo_alternate,
                                  size: 20),
                              label: Text(
                                s.addImage,
                                style: AppTextStyles.font14Bold
                                    .copyWith(color: Colors.white),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: context.accentColor,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Images Grid
                          if (_imageGallery.isEmpty)
                            Container(
                              height: 120,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color:
                                    context.accentColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: context.accentColor
                                      .withValues(alpha: 0.3),
                                  width: 2,
                                  style: BorderStyle.solid,
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.cloud_upload_outlined,
                                    size: 48,
                                    color: context.accentColor,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    s.tapToUploadImages,
                                    style: AppTextStyles.font14Medium.copyWith(
                                      color: context.secondaryTextColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            )
                          else
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                childAspectRatio: 1,
                              ),
                              itemCount: _imageGallery.length,
                              itemBuilder: (context, index) {
                                final image = _imageGallery[index];
                                return Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Stack(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: Image.file(
                                          image,
                                          width: double.infinity,
                                        ),
                                      ),
                                      Positioned(
                                        bottom: 4,
                                        left: 4,
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.black
                                                .withValues(alpha: 0.7),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            '${index + 1}',
                                            style: AppTextStyles.font10Bold
                                                .copyWith(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                        ],
                      ),
                    ),
                    isActive: _currentStep == 2,
                  ),
                  // Step 4: Available Services
                  Step(
                    title: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.room_service_outlined,
                            color: context.accentColor,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          s.availableServices,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    content: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: context.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: context.cardShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            s.selectServices,
                            style: AppTextStyles.font14Medium.copyWith(
                              color: context.secondaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildEnhancedFacilityIcons(),
                        ],
                      ),
                    ),
                    isActive: _currentStep == 3,
                  ),
                  // Step 5: Pricing
                  Step(
                    title: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.attach_money,
                            color: context.accentColor,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          s.pricing,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    content: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: context.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: context.cardShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildPriceField(
                            _dailyPriceController,
                            s.dailyPrice,
                            _includeCommissionDaily,
                            (val) =>
                                setState(() => _includeCommissionDaily = val),
                          ),
                          _buildPriceField(
                            _weeklyPriceController,
                            s.weeklyPrice,
                            _includeCommissionWeekly,
                            (val) =>
                                setState(() => _includeCommissionWeekly = val),
                          ),
                          _buildPriceField(
                            _monthlyPriceController,
                            s.monthlyPrice,
                            _includeCommissionMonthly,
                            (val) =>
                                setState(() => _includeCommissionMonthly = val),
                          ),
                        ],
                      ),
                    ),
                    isActive: _currentStep == 4,
                  ),
                  // Step 6: Booking Details & Policies
                  Step(
                    title: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.policy_outlined,
                            color: context.accentColor,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          s.bookingDetails,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    content: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: context.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: context.cardShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Property Details Section
                          Text(
                            'Property Details',
                            style: AppTextStyles.font16Bold.copyWith(
                              color: context.primaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          Row(
                            children: [
                              Expanded(
                                child: _buildEnhancedTextField(
                                  controller: _bathroomsController,
                                  label: s.numberOfBathrooms,
                                  icon: Icons.bathtub_outlined,
                                  context: context,
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildEnhancedTextField(
                                  controller: _bedroomsController,
                                  label: s.numberOfBedrooms,
                                  icon: Icons.bed_outlined,
                                  context: context,
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          _buildEnhancedTextField(
                            controller: _guestsController,
                            label: s.numberOfGuests,
                            icon: Icons.people_outline,
                            context: context,
                            keyboardType: TextInputType.number,
                          ),

                          const SizedBox(height: 24),

                          // Policies Section
                          Text(
                            'Policies',
                            style: AppTextStyles.font16Bold.copyWith(
                              color: context.primaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          _buildEnhancedTextField(
                            controller: _bookingPolicyController,
                            label: s.bookingPolicy,
                            icon: Icons.rule_outlined,
                            context: context,
                            maxLines: 3,
                          ),

                          const SizedBox(height: 16),

                          _buildEnhancedTextField(
                            controller: _cancellationPolicyController,
                            label: s.cancellationPolicy,
                            icon: Icons.cancel_outlined,
                            context: context,
                            maxLines: 3,
                          ),
                        ],
                      ),
                    ),
                    isActive: _currentStep == 5,
                  ),
                ],
              ),
            ),
          ),

          // Floating Action Buttons
          _buildFloatingActionButtons(context, s),
        ],
      ),
      // Enhanced bottom navigation
      bottomNavigationBar: _buildBottomNavigationBar(context, s),
    );
  }
}
