<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\FacilitiesController;
use App\Http\Controllers\API\FavoriteController;
use App\Http\Controllers\API\GeneralController;
use App\Http\Controllers\API\ItemsController;
use App\Http\Controllers\API\ReservationController;
use App\Http\Controllers\API\ReviewController;
use App\Http\Controllers\API\ServiceCategoryController;
use App\Http\Controllers\HostController;
use Illuminate\Support\Facades\Route;

/*
  |--------------------------------------------------------------------------
  | API Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register API routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | is assigned the "api" middleware group. Enjoy building your API!
  |
 */

// api routes
Route::name('api.')->group(function () {

    // Auth
    Route::prefix('client')->group(function () {
        Route::post('/validate_otp', [AuthController::class, 'validate_otp']);
        Route::post('/resend_otp', [AuthController::class, 'resend_otp']);
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/login_token', [AuthController::class, 'login_token']);
        Route::post('/login_guest', [AuthController::class, 'login_guest']);
    });
    Route::group(['middleware' => ['auth:api']], function () {
        // Protected client routes
        Route::prefix('client')->group(function () {
            Route::delete('/delete_account', [AuthController::class, 'delete_account']);
            Route::post('/logout', [AuthController::class, 'logout']);
            Route::post('/validate', [AuthController::class, 'validate_token']);
            Route::post('/edit_profile', [AuthController::class, 'edit_profile']);
            Route::post('/follow', [AuthController::class, 'follow']);
            Route::delete('/unfollow', [AuthController::class, 'unfollow']);
            Route::get('/follow_list', [AuthController::class, 'follow_list']);
            Route::get('/info', [AuthController::class, 'client_info']);
            Route::post('/notification', [AuthController::class, 'notification']);
        });
        // Settings
        Route::get('/settings', [GeneralController::class, 'settings']);

        // Favorite
        Route::prefix('favorite')->group(function () {
            Route::get('/list', [FavoriteController::class, 'list']);
            Route::post('/set', [FavoriteController::class, 'set']);
        });
        // Reservation
        Route::prefix('reservations')->group(function () {
            Route::get('/list', [ReservationController::class, 'list']);
            Route::post('/check', [ReservationController::class, 'check']);
            Route::post('/create', [ReservationController::class, 'create']);
            Route::get('/{id}', [ReservationController::class, 'show']);
            Route::delete('/cancel/{id}', [ReservationController::class, 'cancel']);
            Route::post('/confirm/{id}', [ReservationController::class, 'confirm'])->middleware('user.host');
        });

        // General
        Route::prefix('general')->group(function () {
            Route::post('/cities', [GeneralController::class, 'cities_geo']);
        });
        // Facilities
        Route::prefix('facilities')->group(function () {
            Route::get('/list', [FacilitiesController::class, 'get_list']);
        });
        // Service Category
        Route::prefix('service_categories')->group(function () {
            Route::get('/list', [ServiceCategoryController::class, 'get_list']);
        });
        // Items
        Route::prefix('items')->group(function () {
            Route::get('/list', [ItemsController::class, 'get_list']);
            Route::get('/search', [ItemsController::class, 'search']);
            Route::post('/create', [ItemsController::class, 'create'])->middleware('user.host');
            Route::put('/update/{id}', [ItemsController::class, 'update'])->middleware(['user.host', 'item.owned']);
            Route::post('/upload-gallery/{id}', [ItemsController::class, 'uploadGallery'])->middleware(['user.host', 'item.owned']);
        });

        // Host Dashboard
        Route::prefix('host')->middleware('user.host')->group(function () {
            Route::get('/dashboard', [HostController::class, 'dashboard']);
            Route::get('/financial-summary', [HostController::class, 'financialSummary']);
            Route::get('/reservations', [HostController::class, 'reservations']);
            Route::get('/reviews', [HostController::class, 'reviews']);

            // Withdrawal Management
            Route::prefix('withdrawal')->group(function () {
                Route::post('/request', [HostController::class, 'requestWithdrawal']);
                Route::get('/history', [HostController::class, 'withdrawalHistory']);
                Route::get('/methods', [HostController::class, 'withdrawalMethods']);
                Route::delete('/cancel/{id}', [HostController::class, 'cancelWithdrawal']);
            });
        });

        // Reviews
        Route::prefix('reviews')->group(function () {
            Route::get('/', [ReviewController::class, 'index']);
            Route::get('/stats/{propertyId}', [ReviewController::class, 'stats']);
            Route::post('/', [ReviewController::class, 'store']);
            Route::put('/{reviewId}', [ReviewController::class, 'update']);
            Route::delete('/{reviewId}', [ReviewController::class, 'destroy']);
            Route::get('/user', [ReviewController::class, 'userReviews']);
            Route::get('/host', [ReviewController::class, 'hostReviews'])->middleware('user.host');
            Route::post('/{reviewId}/like', [ReviewController::class, 'toggleLike']);
            Route::post('/{reviewId}/report', [ReviewController::class, 'report']);
            Route::get('/can-review', [ReviewController::class, 'canReview']);
        });
    });
});

Route::fallback(function () {
    abort(404, 'API resource not found');
});
